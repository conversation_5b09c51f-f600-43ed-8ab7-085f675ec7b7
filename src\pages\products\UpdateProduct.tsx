import React, { useEffect, useState } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import Select, { MultiValue } from "react-select";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import ImageUploadComponent from "components/ImageUploadComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { CustomizationInterface, MenuCategoryInterface, ProductAddInterface } from "interfaces";
import { menuService, customizationService, itemService, productService } from "services";
import CustomizationSelect from "components/ProductCustomizer/CustomizationSelect";
import { useNavigate, useParams } from "react-router-dom";
import './Product.css';
import { Tooltip } from "react-tooltip";
import { FaInfoCircle } from "react-icons/fa";
import "react-tooltip/dist/react-tooltip.css";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import CategoryAccordion from "./CategoryAccordion";


// Helper function to create validation schema
const createValidationSchema = (categoryData: any) =>
  yup.object().shape({
    title: yup.string()
      .trim()
      .required("Name is required")
      .min(3, "Name must be at least 3 characters")
      .max(50, "Name cannot exceed 50 characters"),
    image: yup.string().required("Image is required"),
    backgroundImage: yup.string().required("Background Image is required"),
    type: yup.string().required("Type is required"),
    productType: yup.string().required("Product Type is required"),
    description: yup.string()
      .trim()
      .min(3, "Description must be at least 3 characters")
      .max(200, "Description cannot exceed 200 characters"),
    price: yup
      .number()
      .required("Price is required")
      .positive("Price must be a positive number")
      .min(0, "Price must be greater than or equal to 0")
      .max(999999, "Price must be lower than or equal to 999999"),
    isSuggested: yup.boolean(),
    isAllergyPopUp: yup.boolean(),
    suggestionProduct: yup
      .array()
      .when("isSuggested", {
        is: (value: any) => value, // Checks if isSuggested is true
        then: () => yup
          .array()
          .of(yup.number().required("Suggestion ID is required"))
          .required("Suggested products are required")
          .min(1, "At least one suggestion is required"), // Ensures there is at least one item in the array
        otherwise: () => yup.array().nullable(),
      }),
    menuCategoryId: yup
      .number()
      .required("Menu Category is required")
      .positive("Menu Category must be selected"),
    productCustomizations: yup
      .array()
      .of(
        yup.object().shape({
          id: yup.number().required("Customization ID is required"),
          items: yup
            .array()
            .of(
              yup.object().shape({
                itemId: yup.number().required("Item ID is required"),
                isAdded: yup.boolean().required("Item addition status is required"),
              })
            )
            .required("Items are required")
            .min(0),
          maxItemsAllowed: yup.number(),
          maxItems: yup.number().min(0, "Max items must be zero or greater"),
          isRequired: yup.boolean().required(),
        })
      )
      .min(0, "At least one customization is required"),
  });

const useProductData = () => {

  const [data, setData] = useState<{
    menuCategories: MenuCategoryInterface[];
    customizationOptions: any[];
    itemsList: any[];
    suggestionProducts: any[];
  }>({
    menuCategories: [],
    customizationOptions: [],
    itemsList: [],
    suggestionProducts: [],
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [menus, customizations, items, suggestionProducts] = await Promise.all([
          menuService.getParentMenuCategories({ page: 1, pagination: false, activeStatus: "ALL", isParent: false }),
          customizationService.list({ page: 1, pagination: false, activeStatus: "ALL" }),
          itemService.list({ page: 1, pagination: false, activeStatus: "ALL" }),
          productService.suggestionProductList({ page: 1, pagination: false, activeStatus: "ALL" }),
        ]);


        const groupedData = items?.data?.data?.item.reduce((acc: any, item: any) => {
          const { customizationId, id, title } = item;

          // Find existing group or create a new one
          let group = acc.find((g: any) => g.customizationId === customizationId);
          if (!group) {
            group = { customizationId, list: [] };
            acc.push(group);
          }

          // Push transformed object to the list
          group.list.push({ id, value: id, label: title });

          return acc;
        }, []);


        setData({
          menuCategories: menus?.data?.data?.menuCategory || [],
          customizationOptions: customizations?.data?.data?.customization?.map((c: CustomizationInterface) => ({
            id: c.id,
            value: c.type,
            label: c.title,
          })) || [],
          itemsList: groupedData || [],
          suggestionProducts: suggestionProducts.data.data.product || [],
        });
      } catch (error) {
        toast.error("Failed to load data");
      }
    };

    fetchData();
  }, []);

  return data;
};




// Main component
const UpdateProduct: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>(); // Get the product ID from route parameters
  const { menuCategories, customizationOptions, itemsList, suggestionProducts } = useProductData();
  const [selectedCategoryType, setSelectedCategoryType] = useState<string>("")
  const [selectedCustomization, setSelectedCustomization] = useState<
    { id: string; label: string; value: string }[]
  >([]);
  const [items, setItems] = useState<{
    [key: string]: {
      value: string;
      label: string;
      selected?: boolean;
      quantity?: number;
    }[];
  }>({});

  const [subMenuCategories, setSubMenuCategories] = useState<MenuCategoryInterface[]>([]);

  const handleQuantityAddedChange = (
    customizationId: string,
    itemId: number,
    isAddedValue: boolean
  ) => {
    // Get the index of the customization in Formik's values
    const customizationIndex = formik.values.productCustomizations.findIndex(
      (c) => c.id === customizationId
    );

    if (customizationIndex !== -1) {
      // Get the current customization
      const customization = formik.values.productCustomizations[customizationIndex];

      // Update the items for this customization
      const updatedItems = customization.items.map((item: { itemId: number; }) =>
        item.itemId === itemId
          ? { ...item, isAdded: isAddedValue }
          : item
      );

      // Update Formik's state
      formik.setFieldValue(
        `productCustomizations[${customizationIndex}]`,
        { ...customization, items: updatedItems }
      );
    }
  };



  // Fetch sub menu categories based on the selected menu category
  const fetchSubMenuCategories = async (parentId: number) => {
    try {
      const { data } = await menuService.getSubMenuCategories(parentId);
      setSubMenuCategories(data.data.menuCategory || []);
    } catch (error) {
      toast.error("Failed to load sub menu categories");
    }
  };

  const [initialValues, setInitialValues] = React.useState({
    title: "",
    image: "",
    backgroundImage: "",
    type: "PRESETS",
    productType: "SWEET",
    price: 0,
    isCustomizable: false,
    isTreatYourSelf: false,
    isSuggested: false,
    isAllergyPopUp: false,
    description: "",
    menuCategoryId: 0,
    subMenuCategoryId: 0,
    suggestionProduct: [] as any[],
    productCustomizations: [] as any[],
    needAssistant: false
  });

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const { data: { data: { product } } } = await productService.getDetail(id!); // Fetch item data by ID
        const fetchedProduct = product;
        if (product.menuCategoryId) {
          fetchSubMenuCategories(product.menuCategoryId);
        }

        // Map productCustomizations to the format needed for formik
        const formattedCustomizations = product.productCustomizations.map((customization: any) => ({
          id: customization.id,
          value: customization.value,
          isRequired: customization.isRequired,
          maxItems: customization.maxItems,
          maxItemsAllowed: customization.maxItemsAllowed,
          items: customization.items.map((item: any) => ({
            itemId: item.itemId,
            isAdded: item.isAdded,
          })),
        }));

        setSelectedCustomization(
          product.productCustomizations.map((key: any) => ({
            id: key.id,
            label: key.label,
            value: key.value,
          }))
        );
        const result = product.productCustomizations.reduce((acc: any, customization: any) => {
          acc[customization.label] = customization.items.map((item: any) => ({
            value: item.itemId,
            label: item.label,
            selected: item.isAdded,
            itemType: item.itemType,
          }));
          return acc;
        }, {});

        setItems(
          result
        );
        setSelectedCategoryType(menuCategories?.find((response: any) => response?.id === Number(fetchedProduct?.menuCategoryId))?.type || "");
        setInitialValues({
          ...fetchedProduct,
          productCustomizations: formattedCustomizations,
        });
      } catch (error) {
        toast.error("Failed to fetch item data");
      }
    };
    fetchProduct();
  }, [id, menuCategories]);

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: createValidationSchema(subMenuCategories),
    onSubmit: async (values) => {
      try {
        // Filter out customizations with empty items array
        const filteredCustomizations = values.productCustomizations.filter(
          (customization) => customization.items && customization.items.length > 0
        );

        // Create a new values object with filtered customizations
        const updatedValues = {
          ...values,
          productCustomizations: filteredCustomizations,
        };

        // return ;
        // Implement submit logic here
        await productService.update(id!, updatedValues as unknown as ProductAddInterface);
        toast.success("Product updated successfully");
        navigate('/products')
      } catch (err) {
        toast.error("Failed to update product");
      }
    },
  });
  const handleCustomizationSelection = (
    selected: MultiValue<{ id: string; value: string; label: string }>
  ) => {

    // Convert selected options to a map for quick lookup
    const selectedCustomizationMap = new Map(
      selected.map((item) => [item.id, { id: item.id, label: item.label, value: item.value }])
    );

    // Get current customizations from Formik
    const currentCustomizations = formik.values.productCustomizations;

    // Create a map for current customizations for quick lookup
    const currentCustomizationMap = new Map(
      currentCustomizations.map((c) => [c.id, c])
    );

    // Filter out customizations that are not in the selected list
    const updatedCustomizations = currentCustomizations.filter((customization) =>
      selectedCustomizationMap.has(customization.id)
    );

    // Add new customizations that were not previously in the form state
    selected.forEach((option) => {
      if (!currentCustomizationMap.has(option.id)) {
        updatedCustomizations.push({
          id: option.id,
          items: [],
          maxItems: 0,
          isRequired: false,
        });
      }
    });

    // Update Formik's field value
    formik.setFieldValue("productCustomizations", updatedCustomizations);

    // Update local state for selected customizations
    setSelectedCustomization(
      selected.map((item) => ({
        id: item.id,
        label: item.label,
        value: item.value,
      }))
    );
  };

  const handleCustomization = (
    selectedItems: MultiValue<{ value: string; label: string }>,
    customization: { id: string; value: string; label: string },
    customizationIndex: number,
  ) => {
    const itemsForCustomization = selectedItems.map((item: any) => ({
      value: item.value,
      label: item.label,
      isSelected: item.selected || false,
      itemType: item.itemType || "STANDARD", // Default to "STANDARD" if itemType is not provided
    }));

    // Update local state for items
    setItems((prevItems: any) => {
      return ({
        ...prevItems,
        [customization.value]: itemsForCustomization,
      })
    });

    // Retrieve existing customization from Formik's state
    const existingCustomization = formik.values.productCustomizations.find(
      (c) => c.id === customization.id
    );

    // Create payload with updated values
    const payload = {
      id: customization.id,
      items: selectedItems.map((item) => ({
        itemId: item.value,
        isAdded: false
      })),
      maxItems: existingCustomization ? existingCustomization.maxItems : 0,
      isRequired: existingCustomization ? existingCustomization.isRequired : false,
    };

    // Update Formik's state
    updateCustomizationsItems(payload, customizationIndex);
  };


  const updateCustomizationsItems = (
    customizationValue: any,
    index: number
  ) => {
    const updatedCustomizations = [...formik.values.productCustomizations];
    updatedCustomizations[index] = customizationValue;
    formik.setFieldValue("productCustomizations", updatedCustomizations);
  };

  const handleCheckboxChange = (
    customizationId: string,
    isRequired: boolean,
    index: number
  ) => {
    // Find the customization index in Formik's values
    const customizationIndex = formik.values.productCustomizations.findIndex(
      (c) => c.id === customizationId
    );

    if (customizationIndex !== -1) {
      // Get the current customization from Formik's values
      const customization = formik.values.productCustomizations[customizationIndex];

      // Update the customization with the new isRequired value
      const updatedCustomization = {
        ...customization,
        isRequired,
      };

      // Update Formik's state with the new customization
      const updatedCustomizations = [...formik.values.productCustomizations];
      updatedCustomizations[customizationIndex] = updatedCustomization;

      formik.setFieldValue("productCustomizations", updatedCustomizations);
    }
  };

  // Handle menu category change and fetch corresponding subcategories
  const handleMenuCategoryChange = (selectedOption: any) => {
    setSelectedCategoryType(menuCategories?.find((response: any) => response?.id === Number(selectedOption?.value))?.type || "");
    const menuCategoryId = selectedOption?.value || null;
    formik.setFieldValue("menuCategoryId", Number(menuCategoryId));
    formik.setFieldValue("subMenuCategoryId", ""); // Reset subMenuCategoryId on parent change
    if (menuCategoryId) {
      fetchSubMenuCategories(menuCategoryId);
    }
  };

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isEdit: true, entityPath: "/products", entities: "Products" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{translate("product.edit")}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <FloatingLabel controlId="title" label={translate("product.title")} className="mb-3">
                  <Form.Control
                    type="text"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.title && !!formik.errors.title}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.title && formik.errors.title}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="type" label="Select Type" className="mb-3">
                  <Form.Select
                    name="type"
                    value={formik.values.type}
                    onChange={(value: any) => {
                      if (value?.target?.value !== "PRESETS") {
                        formik.setFieldValue("isTreatYourSelf", false)
                      }
                      formik.handleChange(value)
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.type && !!formik.errors.type}
                  >
                    <option value="PRESETS">{translate("product.presets")}</option>
                    <option value="CREATEOWN">{translate("product.createown")}</option>
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.type && formik.errors.type}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel
                  controlId="description"
                  label={translate("product.description")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={
                      formik.touched.description && !!formik.errors.description
                    }
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.description && formik.errors.description}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="price" label={translate("product.price")} className="mb-3">
                  {/* <Form.Control
                    type="number"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    name="price"
                    value={formik.values.price}
                    onChange={(e) => {
                      // Allow only digits by filtering out non-numeric characters
                      const value = e.target.value.replace(/\D/g, '');
                      formik.setFieldValue("price", value);
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.price && !!formik.errors.price}
                  /> */}
                  <Form.Control
                    type="text" // Use "text" to prevent browser issues with number inputs
                    inputMode="decimal"
                    pattern="[0-9]*[.,]?[0-9]*"
                    name="price"
                    value={formik.values.price}
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only numbers and a single decimal point
                      value = value.replace(/[^0-9.]/g, '');

                      // Ensure only one decimal point is present
                      if (value.split('.').length > 2) {
                        value = value.slice(0, value.lastIndexOf('.'));
                      }

                      // Ensure the first character isn't a decimal point (e.g., ".5" → "0.5")
                      if (value.startsWith('.')) {
                        value = '0' + value;
                      }

                      formik.setFieldValue("price", value);
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.price && !!formik.errors.price}
                  />

                  <Form.Control.Feedback type="invalid">
                    {formik.touched.price && formik.errors.price}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
            </Row>

            <Row>

              <Col md="6" className="d-flex align-items-center mt-2">
                <Col md="6" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isSuggested"
                    name="isSuggested"
                    label="Suggested"
                    checked={formik.values.isSuggested}
                    onChange={(value) => {
                      formik.setFieldValue("suggestionProduct", [])
                      formik.handleChange(value)
                    }}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isSuggested-tooltip"
                      data-tooltip-content="isSuggested info"
                    />
                    <Tooltip
                      id="isSuggested-tooltip"
                      place="top"
                    />
                  </div>
                </Col>
                {
                  // formik.values.type !== "PRESETS" &&
                  <Col md="6" className="d-flex align-items-center">
                    <Form.Check
                      type="checkbox"
                      id="isCustomizable"
                      name="isCustomizable"
                      label="Customizable"
                      checked={formik.values.isCustomizable}
                      onChange={formik.handleChange}
                    />
                    <div className="d-flex align-items-center ms-2">
                      <FaInfoCircle
                        className="info-icon"
                        data-tooltip-id="isCustomizable-tooltip"
                        data-tooltip-content={`${translate("product.customizableInfo")}`}
                      />
                      <Tooltip
                        id="isCustomizable-tooltip"
                        place="top"
                      />
                    </div>
                  </Col>
                }


                <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isTreatYourSelf"
                    name="isTreatYourSelf"
                    label="Treat YourSelf"
                    checked={formik.values.isTreatYourSelf}
                    onChange={formik.handleChange}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isTreatYourSelf-tooltip"
                      data-tooltip-content="isTreatYourSelf info"
                    />
                    <Tooltip
                      id="isTreatYourSelf-tooltip"
                      place="top"
                    />
                  </div>
                </Col>

                <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="needAssistant"
                    name="needAssistant"
                    label={translate("product.needAssist")}
                    checked={formik.values.needAssistant}
                    onChange={(e: any) => {
                      formik.setFieldValue("needAssistant", e?.target?.checked)
                    }}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="needAssistant-tooltip"
                      data-tooltip-content={`${translate("product.needAssistInfo")}`}
                    />
                    <Tooltip
                      id="needAssistant-tooltip"
                      place="top"
                    />
                  </div>
                </Col>


                <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isAllergyPopUp"
                    name="isAllergyPopUp"
                    label="Allergy Popup"
                    checked={formik.values.isAllergyPopUp}
                    onChange={(e) => {
                      formik.setFieldValue("isAllergyPopUp", e.target.checked);
                    }}
                  />

                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isAllergyPopUp-tooltip"
                      data-tooltip-content={`${translate("product.isAllergyPopUp")}`}
                    />
                    <Tooltip
                      id="isAllergyPopUp-tooltip"
                      place="top"
                    />
                  </div>
                </Col>

              </Col>


              <Col md="12" className="mt-4">
                <FloatingLabel controlId="productType" label="Product Type" className="mb-3">
                  <Form.Select
                    name="productType"
                    value={formik.values.productType}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.productType && !!formik.errors.productType}
                  >
                    <option value="SWEET">{translate("product.sweet")}</option>
                    <option value="SPICY">{translate("product.spicy")}</option>
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.productType && formik.errors.productType}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("product.upload-image")} (335px x 265px)</Form.Label>
                  <ImageUploadComponent
                    bucketType="products"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.image}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("image", "")}
                    fieldName="image"
                  />
                  {formik.touched.image && formik.errors.image && (
                    <small className="text-danger">{formik.errors.image}</small>
                  )}
                </Form.Group>
              </Col>
              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("product.background-image")}</Form.Label>
                  <ImageUploadComponent
                    bucketType="products"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.backgroundImage}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("backgroundImage", "")}
                    fieldName="backgroundImage"
                  />
                  {formik.touched.backgroundImage && formik.errors.backgroundImage && (
                    <small className="text-danger">{formik.errors.backgroundImage}</small>
                  )}
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={`${["TAKE_HOME_TREAT"].includes(selectedCategoryType) ? "6" : "12"}`}>
                <FloatingLabel controlId="menuCategoryId" label={translate("product.selectMenuCategories")} className="mb-3">
                  <Form.Select
                    name="menuCategoryId"
                    value={formik.values.menuCategoryId}
                    onChange={(e) => handleMenuCategoryChange({ value: e.target.value })}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.menuCategoryId && !!formik.errors.menuCategoryId}
                  >
                    <option value={0} key={0}>Select</option>
                    {menuCategories.map((cat) => (
                      <option value={cat.id} key={cat.title}>
                        {cat.title}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.menuCategoryId && formik.errors.menuCategoryId}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
            </Row>

            {
              formik?.values?.isSuggested &&
              <Row>
                <Col md="12">
                  <h4>{translate("product.selectSuggestionProduct")}</h4>
                  <Select
                    className="mb-4"
                    isMulti
                    options={suggestionProducts.map((sp) => ({
                      value: sp.id,
                      label: sp.title,
                    }))}
                    onChange={(selectedOptions) =>
                      formik.setFieldValue(
                        "suggestionProduct",
                        selectedOptions ? selectedOptions.map(option => option.value) : []
                      )
                    }
                    value={suggestionProducts
                      .filter((sp) =>
                        (formik.values.suggestionProduct || []).includes(sp.id)
                      )
                      .map((sp) => ({
                        value: sp.id,
                        label: sp.title,
                      }))}
                  />
                  {formik.touched.suggestionProduct &&
                    Array.isArray(formik.errors.suggestionProduct) &&
                    formik.errors.suggestionProduct.length > 0 && (
                      <div>
                        {formik.errors.suggestionProduct.map((error: any, index) => (
                          <small key={index} className="text-danger">
                            {error}
                          </small>
                        ))}
                      </div>
                    )}

                </Col>
                {formik.touched.suggestionProduct && formik.errors.suggestionProduct && (
                  <small className="text-danger">
                    {Array.isArray(formik.errors.suggestionProduct)
                      ? formik.errors.suggestionProduct.join(', ')
                      : typeof formik.errors.suggestionProduct === 'string'
                        ? formik.errors.suggestionProduct
                        : 'Invalid error format'}
                  </small>
                )}
              </Row>
            }

            <Row>
              <Col md="12">
                <h4>{translate("product.selectCustomizations")}</h4>
                <CustomizationSelect
                  options={customizationOptions}
                  onChange={handleCustomizationSelection}
                  value={selectedCustomization}
                />

                {typeof formik.errors.productCustomizations === "string" && (
                  <div className="text-danger">
                    {formik.errors.productCustomizations}
                  </div>
                )}
                {selectedCustomization.map((acc, index) => (

                  <CategoryAccordion
                    key={acc.id}
                    acc={acc}
                    index={index}
                    itemsList={itemsList}
                    items={items[acc.value] || []}
                    handleCustomization={handleCustomization}
                    handleQuantityAddedChange={handleQuantityAddedChange}
                    handleCheckboxChange={handleCheckboxChange}
                    formik={formik}
                    updateCustomizationsItems={updateCustomizationsItems}
                  />
                ))}
              </Col>
            </Row>

            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default UpdateProduct;
