export const getBreadcrumbs = ({ isAdd = false, isEdit = false, isDetail = false, isSingle = false, isList = false,
    basePath = "/dashboard", // default base path
    entityPath = "",
    entities = "Dashboard",  // default name for entities
}) => {
    const breadcrumbs = [
        {
            title: "Dashboard",
            link: basePath,
            isActive: false,
        },
        {
            title: entities,
            link: entityPath,
            isActive: isList ? true : false,
        },
    ];

    if (isAdd) {
        breadcrumbs.push({
            title: `Add ${entities}`,
            link: `${basePath}/${entityPath}/`,
            isActive: true,
        });
    }

    if (isEdit) {

        breadcrumbs.push({
            title: `Update ${entities}`,
            link: `${basePath}/${entityPath}/`,
            isActive: true,
        });
    }


    if (isDetail) {
        breadcrumbs.push({
            title: `${entities} Details`,
            link: `${basePath}/${entityPath}/`,
            isActive: true,
        });
    }

    if (isSingle) {
        breadcrumbs.forEach((breadcrumb) => {
            breadcrumb.isActive = false;
        });
        breadcrumbs[breadcrumbs.length - 1].isActive = true;
    } else {
        breadcrumbs[breadcrumbs.length - 1].isActive = true;
    }

    return breadcrumbs;
};
