import PropTypes from "prop-types";
import { createContext, useContext, useEffect, useMemo, useState } from "react";
import { myAccountService } from "services";

const UserContext = createContext();

export const useUser = () => {
  return useContext(UserContext);
};

export const UserContextProvider = ({ children }) => {
  const [user, setUser] = useState({});
  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState(null);
  const [avatar, setAvatar] = useState(null);

  const getProfile = async () => {
    try {
      setLoading(true);
      const response = await myAccountService.getProfile();
      setUser(response?.data?.data?.admin);
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      getProfile();
    }
  }, [token]);

  useEffect(() => {
    if (avatar) {
      getProfile();
    }
  }, [avatar]);

  const contextValue = useMemo(
    () => ({
      user,
      setUser,
      loading,
      setToken,
      token,
      setAvatar,
      avatar
    }),
    [user, setUser, loading, setToken, token, setAvatar, avatar]
  );

  return (
    <UserContext.Provider value={contextValue}>{children}</UserContext.Provider>
  );
};

UserContextProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
