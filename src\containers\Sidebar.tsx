import { t } from "i18next";
import { FC, ReactNode, SetStateAction, useState } from "react";
import { NavLink } from "react-bootstrap";
import { isMobile } from "react-device-detect";
import {
  FaAd,
  FaAngleDown,
  FaAngleRight,
  FaCodeBranch,
  FaCog,
  FaEnvelope,
  FaHome,
  FaHotel,
  FaLeaf,
  FaMoneyBill,
  FaMoneyBillWave,
  FaPen,
  FaProductHunt,
  FaShoppingCart,
  FaHamburger,
  FaUsers,
  FaPaperPlane,
  FaPager,
} from "react-icons/fa";
import { Link, useLocation } from "react-router-dom";
import "styles/sidebar.scss";

interface SidebarProps {
  isSidebarCollapsed: boolean;
  setIsSidebarCollapsed: React.Dispatch<SetStateAction<boolean>>;
}

interface MenuItemProps {
  title: string;
  url: string;
  icon: ReactNode;
  children?: MenuItemProps[]; // Define children as an array of MenuItemProps
}

interface SidebarMenuItemProps {
  item: MenuItemProps;
  setIsSidebarCollapsed: React.Dispatch<SetStateAction<boolean>>;
}

const menuItems: MenuItemProps[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: <FaHome />,
  },
  {
    title: "Branches",
    url: "/branches",
    icon: <FaCodeBranch />,
  },
  {
    title: "Menu Layout",
    url: "/menu-layout",
    icon: <FaCodeBranch />,
  },
  {
    title: "Category",
    url: "/categories",
    icon: <FaCodeBranch />,
  },
  {
    title: "Static Pages",
    url: "/static-pages",
    icon: <FaPager />,
  },
  {
    title: "Modifiers",
    url: "/customizations",
    icon: <FaCog />,
  }, {
    title: "Sales Report & Analytics",
    url: "/sales-report",
    icon: <FaCog />,
  },
  {
    title: "Items",
    url: "/items",
    icon: <FaLeaf />,
  },
  {
    title: "Products",
    url: "/products",
    icon: <FaHamburger />
    ,
  },
  {
    title: "Customers",
    url: "/customers",
    icon: <FaUsers />,
  },

  // {
  //   title: "Inventory",
  //   url: "/inventory",
  //   icon: <FaShoppingCart />,
  // },
  // {
  //   title: "Categories",
  //   url: "/categories",
  //   icon: <FaLeaf />,
  // },
  {
    title: "Staffs",
    url: "/staffs",
    icon: <FaUsers />,
  },
  // {
  //   title: "Sales Reports & Analytics",
  //   url: "/sales-report-analytics",
  //   icon: <FaMoneyBill />,
  // },
  // {
  //   title: "Payment Monitoring",
  //   url: "/payment-monitoring",
  //   icon: <FaMoneyBillWave />,
  // },
  {
    title: "Orders",
    url: "/orders",
    icon: <FaEnvelope />,
  },
  {
    title: "Caterings",
    url: "/caterings",
    icon: <FaEnvelope />,
  },
  {
    title: "Notifications",
    url: "/notifications",
    icon: <FaEnvelope />,
  },
  {
    title: "Payment Monitoring",
    url: "/transactions",
    icon: <FaEnvelope />,
  },
  // {
  //   title: "Email & SMS compaign",
  //   url: "/email-sms-compaigns",
  //   icon: <FaEnvelope />,
  // },
  // {
  //   title: "Promo Code Manager",
  //   url: "/promo-code-manager",
  //   icon: <FaAd />,
  // },
  // {
  //   title: "Website Content Manager",
  //   url: "/website-content-manager",
  //   icon: <FaPen />,
  // },
  {
    title: "Settings",
    url: "/settings",
    icon: <FaCog />,
  },


  {
    title: "Promo Code",
    url: "/promo-code",
    icon: <FaCodeBranch />,
  },
];

const SidebarMenuItem: FC<SidebarMenuItemProps> = ({
  item,
  setIsSidebarCollapsed,
}) => {
  const location = useLocation();
  const [isMenuItemCollapsed, setIsMenuItemCollapsed] = useState(true);

  const handleToggle = () => {
    setIsMenuItemCollapsed(!isMenuItemCollapsed);
  };

  const handleMenuClick = () => {
    isMobile && setIsSidebarCollapsed(false);
  };

  return (
    <li>
      {item.children ? (
        <>
          <NavLink onClick={handleToggle}>
            {item.icon}
            <span>{item.title}</span>
            {item.children && (
              <span className="menu-arrow">
                {isMenuItemCollapsed ? <FaAngleRight /> : <FaAngleDown />}
              </span>
            )}
          </NavLink>
          <ul
            className={`${isMenuItemCollapsed ? "collapsed" : "collapsed in"}`}
          >
            {item.children.map((childItem) => (
              <SidebarMenuItem
                key={`${item.title}-${item.url}`}
                item={childItem}
                setIsSidebarCollapsed={setIsSidebarCollapsed}
              />
            ))}
          </ul>
        </>
      ) : (
        <Link
          to={item.url}
          className={`${location.pathname === item.url || location.pathname.startsWith(`${item.url}/`) ? "active" : ""}`}
          onClick={() => handleMenuClick()}
        >
          {item.icon}
          <span>{item.title}</span>
          {item.children && (
            <span className="menu-arrow">
              {isMenuItemCollapsed ? <FaAngleRight /> : <FaAngleDown />}
            </span>
          )}
        </Link>
      )}
    </li>
  );
};

const Sidebar: FC<SidebarProps> = ({
  isSidebarCollapsed,
  setIsSidebarCollapsed,
}) => {
  return (
    <div className={`sidebar-custom ${isSidebarCollapsed ? "collapsed" : ""}`}>
      <ul>
        {menuItems.map((item) => (
          <SidebarMenuItem
            key={item.title}
            item={item}
            setIsSidebarCollapsed={setIsSidebarCollapsed}
          />
        ))}
      </ul>
    </div>
  );
};
export default Sidebar;
