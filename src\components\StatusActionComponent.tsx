import { FC } from "react";
import { But<PERSON> } from "react-bootstrap";
import { FaBan, FaCheck } from "react-icons/fa";
import useTranslate from "hooks/useTranslate";

const StatusActionComponent: FC<{
  id: number;
  isSuspended: boolean;
  updateStatus: (id: number, isSuspended: boolean) => void;
}> = ({ id, isSuspended, updateStatus }) => {
  const { translate } = useTranslate();
  return (
    <Button
      size="sm"
      variant={isSuspended ? "primary" : "secondary"}
      onClick={() => updateStatus(id, isSuspended)}
    >
      {isSuspended ? (
        <FaCheck title={translate("common.activate")} />
      ) : (
        <FaBan title={translate("common.deactivate")} />
      )}
    </Button>
  );
};

export default StatusActionComponent;
