import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Card, Button, ListGroup, Row, Col } from "react-bootstrap";
import { FaEdit } from "react-icons/fa";
import { itemService } from "services";
import { ItemInterface } from "interfaces";
import StatusComponent from "components/StatusComponent";
import ImageModal from "components/ImageModal";
import useTranslate from "hooks/useTranslate";
import './ItemDetail.css';
import BreadcrumbComponent from "components/Breadcrumb";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
// import { itemDetailBreadcrumbs } from "constants/breadcrums";

const ItemDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { translate } = useTranslate();
  const [item, setItem] = useState<ItemInterface | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchItemDetails = async () => {
      setLoading(true);
      try {
        const response = await itemService.getDetail(Number(id));
        setItem(response.data.data.item);
      } catch (error) {
        console.error("Error fetching item details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchItemDetails();
  }, [id]);

  if (loading) {
    return <div className="text-center">{translate("items.loading")}</div>;
  }

  if (!item) {
    return <div className="text-center">{translate("items.notFound")}</div>;
  }

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isDetail: true, entityPath: "/items", entities: "Items" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">{translate("items.details")}</h4>
            <Button
              variant="primary"
              onClick={() => navigate(`/items/${id}/edit`)}
            >
              <FaEdit /> {translate("items.edit")}
            </Button>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="flex-column align-items-start">
            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("items.title")}: {item.title || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("items.description")}: {item.description || "N/A"}</p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("items.price")}: ${item.price || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3 d-flex">
                  <p>{translate("common.status")}: <StatusComponent isSuspended={!!item.isSuspended} /> </p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p><strong>{translate("items.image")}:</strong></p>
                  <ImageModal
                    height={"150px"}
                    thumbnailSrc={item.image}
                    altText={item.title}
                  />
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p><strong>{translate("items.sizes")}:</strong></p>
                  <ListGroup>
                    {item.sizes.length > 0 ? (
                      item.sizes.map((size) => (
                        <ListGroup.Item key={size.id} className="d-flex align-items-center">
                          <ImageModal
                            height={"60px"}
                            thumbnailSrc={size.image}
                            altText={size.title}
                          />{" "}
                          <div className="ms-3">
                            {size.title} - ${size.price}
                          </div>
                        </ListGroup.Item>
                      ))
                    ) : (
                      <ListGroup.Item>{translate("items.noSizes")}</ListGroup.Item>
                    )}
                  </ListGroup>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p><strong>{translate("items.types")}:</strong></p>
                  <ListGroup>
                    {item.types.length > 0 ? (
                      item.types.map((type) => (
                        <ListGroup.Item key={type.id} className="d-flex align-items-center">
                          <ImageModal
                            height={"60px"}
                            thumbnailSrc={type.image}
                            altText={type.title}
                          />{" "}
                          <div className="ms-3">
                            {type.title} - ${type.price}
                          </div>
                        </ListGroup.Item>
                      ))
                    ) : (
                      <ListGroup.Item>{translate("items.noTypes")}</ListGroup.Item>
                    )}
                  </ListGroup>
                </div>
              </Col>
            </Row>

          </div>
        </Card.Body>
      </Card>
      {/* </div> */}
    </>
  );
};

export default ItemDetail;
