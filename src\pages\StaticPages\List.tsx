import AddActionComponent from "components/AddActionComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import SearchInputComponent from "components/SearchInputComponent";
import StatusComponent from "components/StatusComponent";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { StaticService } from "services/static.service";

const StaticPages = () => {
    const [data, setData] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [page, setPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [sortBy, setSortBy] = useState("id");
    const [sortDirection, setSortDirection] = useState("DESC");
    const [searchTerm, setSearchTerm] = useState("");

    const fetchData = async () => {
        setLoading(true);
        const payload = {
            page,
            pagination: true,
            skip: (page - 1) * perPage,
            limit: perPage,
            sortBy,
            sortDirection,
            searchTerm,
        };
        try {
            const response = await StaticService.getAll(payload);
            setData(response.data.data.docs);
            setTotalRows(response.data.data.meta.totalDocs);
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const renderStaffStatus = (isSuspended: boolean) => {
        return <StatusComponent isSuspended={!isSuspended} />;
    };

    const renderActionColumn = (row: any) => {
        return (
            <>
                <EditActionComponent url={`/static-pages/edit/${row.id}`}></EditActionComponent>{" "}
            </>
        );
    };

    const columns = [
        {
            name: "Title",
            sortable: true,
            sortField: "title",
            selector: (row: any) => row.title || "N/A",
        },
        {
            name: "Email",
            sortable: false,
            sortField: "email",
            selector: (row: any) => row.email || "N/A",
        },
        {
            name: "Status",
            sortable: false,
            sortField: "status",
            selector: (row: any) => renderStaffStatus(!row.isSuspended),
        },
        {
            name: "Action",
            selector: (row: any) => renderActionColumn(row),
        },
    ];

    useEffect(() => {
        fetchData();
    }, [page, perPage, sortBy, sortDirection, searchTerm]);

    return (
        <>
            <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/static-pages", entities: "Static Pages" })} />
            <Card className="custom-card">
                <Card.Header>
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex gap-2 align-items-center custom-card-header-title">
                            <FaList />
                            <h4 className="mb-0">Staffs</h4>
                        </div>
                        {/* <div>
                            <AddActionComponent url="/staffs/add" />
                        </div> */}
                    </div>
                </Card.Header>

                <Card.Body>
                    <div className="d-flex gap-3 pb-4 search-box">
                        <SearchInputComponent
                            searchTerm={searchTerm}
                            setSearchTerm={setSearchTerm}
                        />
                    </div>

                    <DatatableComponent
                        columns={columns}
                        data={data}
                        loading={loading}
                        totalRows={totalRows}
                        setPage={setPage}
                        setPerPage={setPerPage}
                        setSortBy={setSortBy}
                        setSortDirection={setSortDirection}
                    />
                </Card.Body>
            </Card>
        </>
    );
};

export default StaticPages;
