import React, { useEffect, useState } from 'react'
import {
    Accordion,
    Col,
    Form,
    Row
} from "react-bootstrap";
import useTranslate from "hooks/useTranslate";
import Select from "react-select";
import ItemList from 'components/ProductCustomizer/ItemList';
import { toast } from 'react-toastify';

const CategoryAccordion = ({ acc, index, itemsList, items, formik, handleCheckboxChange, handleQuantityAddedChange, handleCustomization, updateCustomizationsItems }: any) => {
    const { translate } = useTranslate();
    const [selectedCount, setSelectedCount] = useState(0);

    useEffect(() => {
        const count: any = items.reduce((count: number, item: any) => {
            return count + (item.selected ? 1 : 0); // Increment count if item is added
        }, 0);
        setSelectedCount(count);
    }, [])

    return (
        <Accordion >
            <Accordion.Item eventKey={acc.value}>
                <Accordion.Header>{acc.label}</Accordion.Header>
                <Accordion.Body>
                    <Row>
                        <Col md={3} className="d-flex align-items-center">
                            <Form.Label className="mb-0">
                                {translate("product.isRequired")}
                            </Form.Label>
                            <Form.Check
                                className="ms-2"
                                type="checkbox"
                                checked={
                                    formik.values.productCustomizations.find(
                                        (customization: any) => customization.id === acc.id
                                    )?.isRequired || false
                                }
                                onChange={(e) =>
                                    handleCheckboxChange(
                                        acc.id,
                                        e.target.checked,
                                        index
                                    )
                                }
                            />
                        </Col>

                        <Col md={3} className="d-flex align-items-center">
                            <Form.Label>
                                <p>{translate("product.maxQuantity")}</p>
                            </Form.Label>
                            <Form.Control
                                type="number"
                                inputMode="numeric"
                                pattern="[0-9]*"
                                value={
                                    // Get the current maxItems value from Formik's state
                                    formik.values.productCustomizations.find((c: any) => c.id === acc.id)?.maxItems || ""
                                }
                                onChange={(e) => {

                                    if (selectedCount === 0 || Number(e?.target?.value) >= Number(selectedCount)) {
                                        const value = Number(e.target.value.replace(/\D/g, ''));
                                        // Update Formik's state
                                        const updatedCustomization = formik.values.productCustomizations.find(
                                            (c: any) => c.id === acc.id
                                        );

                                        if (updatedCustomization) {
                                            const updatedCustomizationWithMaxItems = {
                                                ...updatedCustomization,
                                                maxItems: value,
                                            };

                                            const index = formik.values.productCustomizations.findIndex(
                                                (c: any) => c.id === acc.id
                                            );

                                            updateCustomizationsItems(updatedCustomizationWithMaxItems, index);
                                        }
                                    } else {
                                        toast.info(`already selected ${selectedCount} items`)
                                    }
                                }}
                                className="ms-2"
                                style={{ width: "120px" }}
                            />
                        </Col>

                        <Col md={3} className="d-flex align-items-center">
                            <Form.Label>
                                <p>{translate("product.maxSelectection")}</p>
                            </Form.Label>
                            <Form.Control
                                type="number"
                                inputMode="numeric"
                                pattern="[0-9]*"
                                value={
                                    formik.values.productCustomizations.find((c: any) => c.id === acc.id)?.maxItemsAllowed || ""
                                }
                                onChange={(e) => {

                                    const value = Number(e.target.value.replace(/\D/g, ''));

                                    const updatedCustomization = formik.values.productCustomizations.find(
                                        (c: any) => c.id === acc.id
                                    );

                                    if (updatedCustomization) {
                                        const updatedCustomizationWithMaxItems = {
                                            ...updatedCustomization,
                                            maxItemsAllowed: value,
                                        };

                                        const index = formik.values.productCustomizations.findIndex(
                                            (c: any) => c.id === acc.id
                                        );

                                        updateCustomizationsItems(updatedCustomizationWithMaxItems, index);
                                    }
                                }}
                                className="ms-2"
                                style={{ width: "120px" }}
                            />
                        </Col>


                    </Row>
                    <div className="items-selections">
                        <h4>
                            {translate("product.pleaseSelectCustomizations")}
                        </h4>
                        <Select
                            isMulti
                            className="mb-4"
                            options={itemsList
                                .filter((item: any) => item.customizationId === acc.id) // Filter to get items with matching customizationId
                                .flatMap((item: any) => item.list)} // Return the list of options from the matching item
                            onChange={(selected) => {
                                handleCustomization(selected, acc, index)
                            }}
                            value={
                                items?.map((item: any) => ({
                                    value: item.value,
                                    label: item.label,
                                    itemType: item?.itemType
                                })) || []
                            }
                        />
                        <ItemList
                            items={items || []}
                            selectedCount={selectedCount}
                            maxItems={formik?.values?.productCustomizations[index]?.maxItems}
                            handleAdded={(itemId, isAddedValue) => {
                                setSelectedCount((prevCount) => isAddedValue ? prevCount + 1 : prevCount === 0 ? 0 : prevCount - 1);
                                handleQuantityAddedChange(
                                    acc.id,
                                    itemId,
                                    isAddedValue
                                )
                            }
                            }
                        />
                    </div>
                    {formik.errors.productCustomizations &&
                        Array.isArray(
                            formik.errors.productCustomizations
                        ) && (
                            <div className="text-danger">
                                {formik.errors?.productCustomizations &&
                                    Array.isArray(
                                        formik.errors.productCustomizations
                                    ) &&
                                    formik.errors.productCustomizations[index] && (
                                        <div className="text-danger">
                                            {Object.keys(
                                                formik.errors.productCustomizations[
                                                index
                                                ] as any
                                            ).map((key) => (
                                                <div key={key}>
                                                    {
                                                        (
                                                            formik.errors
                                                                ?.productCustomizations?.[
                                                            index
                                                            ] as any
                                                        )[key]
                                                    }
                                                </div>
                                            ))}
                                        </div>
                                    )}
                            </div>
                        )}
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    )
}

export default CategoryAccordion