.navbar-custom {
  background-color: #fff;
  -webkit-box-shadow: 0 0.5rem 1rem 0 rgb(44 51 73 / 10%);
  box-shadow: 0 0.5rem 1rem 0 rgb(44 51 73 / 10%);
  padding: 0 10px 0 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  z-index: 100;
  &.menu-collapsed{
    .logo-box{
      width: 70px;
    }
  }
  .navbar{
    align-items: center;
    padding: 0px;
    .button-menu{
      svg{
        font-size: 25px;
        fill: #000;
      }
    }
  }
  .navbar-brand{
    img{
      height: 50px;
      max-width: 200px;
    }
  }
  .logo-box{
    height: 70px;
    width: 240px;
    padding: 10px;
  }
  .profile {
    position: absolute;
    top: 0.6rem;
    right: 0.5rem;
    a{
     
      img{
        background: #000;
        border-radius: 50px;
        width: 50px;
        height: 50px;
        padding: 5px;
      }
    }
    
    .dropdown-menu{
      &.show{
        left: auto;
        right: 0px;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        margin: 0px;
        padding: 0px;
      }
      .nav-link{
        padding: 15px 20px;
        display: flex;
        align-items: center;
        &:last-child{
          border-bottom: 0px;
        }
        svg{
          margin-right: 5px;
        }
        &:focus-visible{
          box-shadow: none;
        }
      }
    } 
   
  }
}
@media(max-width:767px){
.navbar-custom {
  .logo-box{
    display: none;
  }
  .topnav-menu{
    position: absolute;
    top: 1rem;
    left: 1rem;
  }
}

}
