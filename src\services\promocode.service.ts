import base from "./base.service";
import { PromoCodeInterface } from "interfaces";


const getPromoCodeList = (payload: any) => base.get("/promo-code", { params: payload });
const add = (payload: PromoCodeInterface) => base.post(`/promo-code`, { ...payload });
const getPromoCodeDetails = (id: number | string) => base.get(`/promo-code/${id}`);
const updatePromoCodeDetails = (id: number | string, payload: any) => base.put(`/promo-code/${id}`, { ...payload });
const deleteMenuLayout = (id: number | string) => base.delete(`/promo-code/${id}`);
const updatePromoCodeStatus = (id: number | string) => base.get(`/promo-code/${id}/update-status`);

export const promoCodeService = {

    getPromoCodeList,
    add,
    getPromoCodeDetails,
    updatePromoCodeDetails,
    deleteMenuLayout,
    updatePromoCodeStatus

};
