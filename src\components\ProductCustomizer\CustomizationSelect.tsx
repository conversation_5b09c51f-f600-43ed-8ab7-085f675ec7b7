import React from 'react';
import Select from 'react-select';

interface CustomizationSelectProps {
  options: { value: string; label: string }[];
  onChange: (selected: any, actionMeta: any) => void;
  value?: { value: string; label: string }[]; // Add value prop for selected options
}

const CustomizationSelect: React.FC<CustomizationSelectProps> = ({ options, onChange, value }) => {
  console.log("first", options, value)
  return (
    <Select
      className='mb-4'
      isMulti
      options={options}
      onChange={onChange}
      value={value} // Set value prop to prefill selected options
    />
  );
};

export default CustomizationSelect;
