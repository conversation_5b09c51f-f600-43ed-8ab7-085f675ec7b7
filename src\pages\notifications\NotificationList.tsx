import AddActionComponent from "components/AddActionComponent";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import ImageModal from "components/ImageModal";
import SearchInputComponent from "components/SearchInputComponent";
import StatusActionComponent from "components/StatusActionComponent";
import StatusComponent from "components/StatusComponent";
import useTranslate from "hooks/useTranslate";
import { CustomizationInterface, ItemInterface } from "interfaces";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { productService } from "services";
import BreadcrumbComponent from "components/Breadcrumb";
// import { productListBreadcrumbs } from "constants/breadcrums";
import ViewActionComponent from "components/ViewActionComponent";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { orderService } from "services/order.service";
import { notificationService } from "services/notification.service";

const NotificationList = () => {

    const { translate } = useTranslate();
    const [data, setData] = useState<CustomizationInterface[]>([]);
    const [loading, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [page, setPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [sortBy, setSortBy] = useState("id");
    const [sortDirection, setSortDirection] = useState("DESC");
    const [searchTerm, setSearchTerm] = useState("");


    const updateStatus = async (id: number) => {
        try {
            const response: any = await orderService.updateStatus(id)
            if (response?.status === 200) {
                setData(prevData =>
                    prevData.map(product =>
                        product.id === id ? { ...product, isSuspended: !product.isSuspended } : product
                    )
                );
            }
        } catch (error) { }

    };

    const renderActionColumn = (row: ItemInterface) => {
        return (
            <>
                <StatusActionComponent
                    id={Number(row.id)}
                    // key={Number(row.id)}
                    isSuspended={Boolean(row.isSuspended)}
                    updateStatus={updateStatus}
                />{" "}

                <EditActionComponent url={`/products/${row.id}/edit`}></EditActionComponent>{" "}

                <ViewActionComponent url={`/products/${row.id}/view`} />
            </>
        );
    };
    const fetchData = async () => {
        setLoading(true);
        const payload = {
            page,
            pagination: true,
            skip: (page - 1) * perPage,
            limit: perPage,
            sortBy,
            sortDirection,
            searchTerm,
            activeStatus: "ALL",
        };
        try {
            const response = await notificationService.list(payload);
            setData(response.data.data.docs);
            setTotalRows(response.data.data.meta.totalDocs);
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const renderCategoryStatus = (isSuspended: boolean) => {
        return <StatusComponent isSuspended={!isSuspended} />;
    };

    const columns = [
        {
            name: translate("product.title"),
            sortable: true,
            sortField: "title",
            selector: (row: CustomizationInterface) => row.title || "N/A",
        },
        {
            name: translate("product.desc"),
            sortable: false,
            sortField: "descri",
            selector: (row: any) => row?.description.length < 27 ? row?.description : row.description.slice(0, 27) + "..." || "N/A",
        },
        {
            name: translate("product.read"),
            sortable: false,
            sortField: "isSuspended",
            selector: (row: any) => row?.isRead === 0 ? "Marked as Read" : "Not Read Yet",
        }
    ];

    useEffect(() => {
        fetchData();
    }, [page, perPage, sortBy, sortDirection, searchTerm]);

    return (
        <>
            <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/notifications", entities: "Notifications" })} />

            {/* <BreadcrumComponent breadcrumbs={productListBreadcrumbs} /> */}
            <Card className="custom-card">
                <Card.Header>
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex gap-2 align-items-center custom-card-header-title">
                            <FaList />
                            <h4 className="mb-0">{translate("common.notifications")}</h4>
                        </div>
                        {/* <div>
                            <AddActionComponent url="/products/add" />
                        </div> */}
                    </div>
                </Card.Header>

                <Card.Body>
                    <div className="d-flex gap-3 pb-4 search-box">
                        <SearchInputComponent
                            searchTerm={searchTerm}
                            setSearchTerm={setSearchTerm}
                        />
                    </div>

                    <DatatableComponent
                        columns={columns}
                        data={data}
                        loading={loading}
                        totalRows={totalRows}
                        setPage={setPage}
                        setPerPage={setPerPage}
                        setSortBy={setSortBy}
                        setSortDirection={setSortDirection}
                    />
                </Card.Body>
            </Card>
        </>
    );
};

export default NotificationList;