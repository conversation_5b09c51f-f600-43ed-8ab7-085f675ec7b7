import React, { useEffect, useState } from "react";
import {
    Card,
    Col,
    Form,
    Row,
    Button,
    FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { useNavigate, useParams } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { StaticService } from "services/static.service";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css"; // Import styles for React Quill

export const quillModules = {
    toolbar: {
        container: [
            [{ 'header': '1' }, { 'header': '2' }, { 'font': [] }],
            [{ 'size': [] }],
            ['bold', 'italic', 'underline', 'strike', 'blockquote'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            ['link'],
            ['clean']
        ],
    },
};

// Helper function to create validation schema
const createValidationSchema = () =>
    yup.object().shape({
        title: yup
            .string()
            .trim()
            .required("Title is required")
            .min(3, "Title must be at least 3 characters")
            .max(50, "Title cannot exceed 50 characters"),
        description: yup.string().required('Description is required'),
    });

// Main component
const AddStaticPage: React.FC = () => {
    const { translate } = useTranslate();
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const [editor, setEditor] = useState<any>(null);

    const [initialValues, setInitialValues] = useState({
        title: "",
        description: "",
    });


    const fetchDetails = async (id: string) => {
        try {
            const response: any = await StaticService.getDetail(id);
            setInitialValues({ title: response?.data?.title, description: response?.data?.description });
        } catch (error) {
            console.error("Error fetching staff:", error);
            toast.error("Failed to fetch staff data");
        }
    };

    useEffect(() => {
        if (id) {
            fetchDetails(id);
        }
    }, [id]);

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: createValidationSchema(),
        onSubmit: async (values: any) => {
            try {
                await StaticService.update({
                    id,
                    title: values.title,
                    description: values.description,
                });
                toast.success("Page updated successfully");
                navigate("/static-pages");
            } catch (err) {
                console.error("Error submitting form:", err);
                toast.error("Failed to submit form");
            }
        },
    });

    return (

        <>
            <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isEdit: true, entityPath: "/static-pages", entities: "Static-Pages" })} />
            <Card className="custom-card">
                <Card.Header>
                    <h4 className="mb-0">{id ? translate("staff.edit") : translate("staff.add")}</h4>
                </Card.Header>
                <Card.Body>
                    <Form onSubmit={formik.handleSubmit}>
                        <Row>
                            <Col md="12">
                                <FloatingLabel
                                    controlId="title"
                                    label={translate("staff.title")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type="text"
                                        name="title"
                                        value={formik.values.title}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={formik.touched.title && !!formik.errors.title}
                                        autoComplete="off"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.title && formik.errors.title}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>

                            <Col md="12">
                                <ReactQuill
                                    style={{ height: '300px', minHeight: '200px', maxHeight: '200px' }}
                                    ref={(el) => setEditor(el)}
                                    value={formik?.values?.description}
                                    onChange={(content, delta, source, editor) => {
                                        const plainText = editor.getText(); // Get plain text without HTML tags
                                        const characterLimit = 4500; // Set your character limit here
                                        if (plainText.length <= characterLimit) {
                                            formik?.setFieldValue("description", content);
                                        } else {
                                            // Prevent exceeding the limit by truncating the content
                                            const trimmedContent = content.slice(0, characterLimit);
                                            formik?.setFieldValue("description", trimmedContent);
                                        }
                                    }}
                                    modules={quillModules}
                                    theme="snow"
                                />
                            </Col>
                            <div style={{ marginTop: "50px", marginLeft: '20px' }}>
                                <small>
                                    {`${formik?.values.description.replace(/<[^>]*>/g, "").length}/${4500} characters used`}
                                </small>
                                {formik?.touched.description && formik?.errors.description && (
                                    <Form.Control.Feedback type="invalid" className="d-block">
                                        {formik?.errors.description}
                                    </Form.Control.Feedback>
                                )}
                            </div>


                            <Col md="12" className="text-end mt-4">
                                <Button variant="primary" type="submit">
                                    Submit
                                </Button>
                            </Col>
                        </Row>
                    </Form>
                </Card.Body>
            </Card>
        </>
    );
};

export default AddStaticPage;
