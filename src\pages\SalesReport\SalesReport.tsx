import { useCallback, useEffect, useMemo, useState } from "react";
import { Row } from "react-bootstrap";
import { FaFirstOrder } from "react-icons/fa";
import BreadcrumbComponent from "components/Breadcrumb";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { DashboardCardProps } from "pages/dashboard/dashboard.card.interface";
import DashboardCards from "pages/dashboard/DashboardCard";
import { branchService } from "services/branch.service";



const SalesReport = () => {
    const [data, setData] = useState<any>(null);

    const fetchData = useCallback(() => {
        branchService
            .getSalesReport()
            .then((res: any) => setData(res?.data?.data?.dashboard))
            .catch((error) => {
                console.error("Error fetching dashboard details:", error);
            });
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // Destructuring `data` to improve readability
    const {
        appSales,
        kioskSales,
        webSales,
    } = data || {};

    // Array of card data to generate cards dynamically
    const cardData = useMemo(
        () => [
            {
                title: appSales,
                subtitle: "App Sale",
                description: "Number of App Sale.",
                icon: <FaFirstOrder />,
                navigate: "#",
            },
            {
                title: kioskSales,
                subtitle: "Kiosk Sale",
                description: "Number of kiosk Sale",
                icon: <FaFirstOrder />,
                navigate: "#",
            },
            {
                title: webSales,
                subtitle: "Web Sale",
                description: "Number of Web Sale",
                icon: <FaFirstOrder />,
                navigate: "#",
            },
        ],
        [
            appSales,
            kioskSales,
            webSales,
        ]
    );

    const dashboardCards = useMemo(
        () => (
            <Row>
                <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isSingle: true, entityPath: "/dashboard", entities: "" })} />
                {cardData.map((config: DashboardCardProps, index: number) => (
                    <DashboardCards
                        key={`dashboard_${index + 10}`}
                        title={config.title}
                        subtitle={config.subtitle}
                        description={config.description}
                        navigate={config.navigate}
                        icon={config.icon}
                    />
                ))}
            </Row>
        ),
        [cardData]
    );

    return <>{dashboardCards}</>;
};

export default SalesReport;
