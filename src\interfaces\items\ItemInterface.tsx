export interface ItemInterface {
  id?: number
  isSuspended?: boolean
  isMultiType: boolean | undefined;
  isMultiSize: boolean | undefined;
  title: string;
  description: string;
  price: number;
  image: string;
  types: ItemTypeInterface[]
  sizes: SizeTypeInterface[]
  isLayering: boolean
  isDouble: boolean
  isAmountSelectionAvailable: boolean
  customizationId: number,
  layerType: string,
}

export interface ItemTypeInterface {
  id?: string | number;
  title: string,
  image: string
  price: number
}

export interface SizeTypeInterface {
  id?: string | number;
  title: string
  image: string
  price: number
}
