export interface ProductInterface {
  id?: number;
  title: string;
  image: string;
  backgroundImage: string;
  type: string;
  productType: string;
  price: number;
  isCustomizable: boolean | undefined;
  isTreatYourSelf: boolean | undefined;
  // isSuggested: boolean | undefined;
  description: string;
  menuCategoryId: number;
  subMenuCategoryId: number | undefined;
  suggestionProduct: number[];
  productCustomizations: Customization[];
  isSuspended?: boolean;
  menuCategoryTitle?: string;
  subMenuCategoryTitle?: string;
  suggestionProductTitle?: string[];
  }

  interface Customization {
    id: number;
    items: Item[];
    maxItems: number;
    isRequired: boolean;
    label?: string;
    value?: string;
  }

  interface Item {
    itemId: number;
    isAdded: boolean;
    label?: string;
  }