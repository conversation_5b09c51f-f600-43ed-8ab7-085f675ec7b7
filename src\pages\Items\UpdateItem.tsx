import React, { useEffect } from "react";
import BreadcrumbComponent from "components/Breadcrumb";
import { useFormik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON>, Card, Col, Floating<PERSON>abel, Form, Row } from "react-bootstrap";
import { toast } from "react-toastify";
import * as yup from "yup";
import ImageUploadComponent from "components/ImageUploadComponent";
import { ItemInterface } from "interfaces";
// import { itemUpdateBreadcrumbs } from "constants/breadcrums";
import ItemTypeRow from "./ItemType";
import ItemSizeRow from "./ItemSize";
import { itemService } from "services"
import alertService from "services/alert.service";
import { Tooltip } from "react-tooltip";
import { FaInfoCircle } from "react-icons/fa";
import "react-tooltip/dist/react-tooltip.css";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

const UpdateItem = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>(); // Get the item ID from route parameters

  const [initialValues, setInitialValues] = React.useState<ItemInterface>({
    title: "",
    description: "",
    price: 0,
    image: "",
    types: [],
    sizes: [],
    isMultiSize: false,
    isMultiType: false,
    isLayering: false,
    isDouble: false,
    isAmountSelectionAvailable: false,
    customizationId: 0,
    layerType: ""
  });

  useEffect(() => {
    const fetchItem = async () => {
      try {
        const { data: { data: { item } } } = await itemService.getDetail(id!); // Fetch item data by ID
        const fetchedItem = item as ItemInterface;
        const isMultiType = fetchedItem.types.length > 0;
        const isMultiSize = fetchedItem.sizes.length > 0;
        setInitialValues({ ...fetchedItem, isMultiSize, isMultiType });
      } catch (error) {
        console.error("Error fetching item:", error);
        toast.error("Failed to fetch item data");
      }
    };

    fetchItem();
  }, [id]);

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: yup.object().shape({
      title: yup.string().trim().required("Name is required")
        .min(3, "Name must be at least 3 characters")
        .max(50, "Name cannot exceed 50 characters"),
      description: yup.string().trim().required("Description is required")
        .min(3, "Description must be at least 3 characters")
        .max(200, "Description cannot exceed 200 characters"),
      price: yup
        .number()
        .required("Price is required")
        .min(0, "Price must be greater than or equal to 0")
        .max(999999, "Price must be lower than or equal to 999999"),
      image: yup.string().required("Image URL is required"),
      types: yup.array().of(
        yup.object().shape({
          title: yup.string().trim().required("Name is required"),
          image: yup.string().required("Image URL is required"),
          price: yup
            .number()
            .required("Price is required")
            .min(0, "Price must be greater than or equal to 0")
            .max(999999, "Price must be lower than or equal to 999999"),
        })
      ),
      sizes: yup.array().of(
        yup.object().shape({
          title: yup.string().trim().required("Name is required"),
          image: yup.string().required("Image URL is required"),
          price: yup
            .number()
            .required("Price is required")
            .min(0, "Price must be greater than or equal to 0")
            .max(999999, "Price must be lower than or equal to 999999"),
        })
      ),
    }),
    onSubmit: async (values) => {
      console.warn({ values });
      // return
      if (values.isMultiType && values.types.length === 0) {
        alertService.showError("Alert", "Please add types if it is a multi-select item");
        return;
      }

      if (values.isMultiSize && values.sizes.length === 0) {
        alertService.showError("Alert", "Please add sizes if it is a multi-size item");
        return;
      }

      try {
        const result = await itemService.updateItem(id!, values); // Update item with ID
        if (!result.data.success) {
          throw new Error("Failed to update item");
        }

        toast.success("Item updated successfully");
        navigate("/items"); // Update navigation path as needed
      } catch (err) {
        console.error("Error updating item:", err);
        toast.error("Failed to update item");
      }
    },
  });

  const handleMultiTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    formik.setFieldValue("isMultiType", isChecked);
    if (!isChecked) {
      formik.setFieldValue("types", []); // Clear types if checkbox is unchecked
    }
  };

  const handleMultiSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    formik.setFieldValue("isMultiSize", isChecked);
    if (!isChecked) {
      formik.setFieldValue("sizes", []); // Clear sizes if checkbox is unchecked
    }
  };

  const addSizeRow = () => {
    const newSizes = [
      ...formik.values.sizes,
      { title: "", image: "", price: 0 },
    ];
    formik.setFieldValue("sizes", newSizes);
  };

  const addItemTypeRow = () => {
    const newTypes = [
      ...formik.values.types,
      { title: "", image: "", price: 0 },
    ];
    formik.setFieldValue("types", newTypes);
  };

  return (
    <>
      {
        console.warn(formik.errors)
      }
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isEdit: true, entityPath: "/items", entities: "Items" })} />
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">Update Item</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row className="align-items-center">
              <Col md="6">
                <FloatingLabel controlId="title" label="Title" className="mb-3">
                  <Form.Control
                    type="text"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.title && formik.errors.title ? (
                    <small className="text-danger">{formik.errors.title}</small>
                  ) : null}
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="description" label="Description" className="mb-3">
                  <Form.Control
                    as="textarea"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.description && formik.errors.description ? (
                    <small className="text-danger">{formik.errors.description}</small>
                  ) : null}
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="price" label="Price" className="mb-3">
                  <Form.Control
                    type="text" // Using "text" instead of "number" to avoid unintended browser behavior
                    inputMode="decimal"
                    pattern="[0-9]*[.,]?[0-9]*"
                    name="price"
                    value={formik.values.price}
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only numbers and a single decimal point
                      value = value.replace(/[^0-9.]/g, ''); // Remove non-numeric characters except "."

                      // Prevent multiple decimal points
                      if (value.split('.').length > 2) {
                        value = value.slice(0, value.lastIndexOf('.'));
                      }

                      formik.setFieldValue("price", value);
                    }}
                    onBlur={formik.handleBlur}
                  />

                  {/* <Form.Control
                    type="number"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    name="price"
                    value={formik.values.price}
                    onChange={(e) => {
                      // Allow only digits by filtering out non-numeric characters
                      const value = e.target.value.replace(/\D/g, '');
                      formik.setFieldValue("price", value);
                    }}
                    onBlur={formik.handleBlur}
                  /> */}
                  {formik.touched.price && formik.errors.price ? (
                    <small className="text-danger">{formik.errors.price}</small>
                  ) : null}
                </FloatingLabel>
              </Col>

              {/* Checkboxes */}
              <Col md="6" className="d-flex align-items-center">
                <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isMultiType"
                    name="isMultiType"
                    label="Multi Type"
                    checked={formik.values.isMultiType}
                    onChange={formik.handleChange}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isMultiType-tooltip"
                      data-tooltip-content="isMultiType info"
                    />
                    <Tooltip
                      id="isMultiType-tooltip"
                      place="top"
                    />
                  </div>
                </Col>

                {/* <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isMultiSize"
                    name="isMultiSize"
                    label="Multi Size"
                    checked={formik.values.isMultiSize}
                    onChange={formik.handleChange}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isMultiSize-tooltip"
                      data-tooltip-content="isMultiSize info"
                    />
                    <Tooltip
                      id="isMultiSize-tooltip"
                      place="top"
                    />
                  </div>
                </Col> */}
              </Col>

              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>Upload Image</Form.Label>
                  <ImageUploadComponent
                    bucketType="items"
                    handleOnChange={(e) => formik.handleChange(e)}
                    image={formik.values.image}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("image", "")}
                    fieldName="image"
                  />
                  {formik.touched.image && formik.errors.image ? (
                    <small className="text-danger">{formik.errors.image}</small>
                  ) : null}
                </Form.Group>
              </Col>
            </Row>

            <Row>
              {/* Item Types Section */}
              {formik.values.isMultiType && (
                <Col md="6">
                  <h5>Item Types</h5>
                  {formik.values.types.map((item: any, index: number) => (
                    <ItemTypeRow
                      key={index}
                      index={index}
                      item={item}
                      changeHandler={(i, value, fieldName) =>
                        formik.setFieldValue(`types[${index}].${fieldName}`, value)
                      }
                      onRemove={(i) =>
                        formik.setFieldValue(
                          "types",
                          formik.values.types.filter((_, i) => i !== index)
                        )
                      }
                      errors={formik?.errors?.types?.[index]}
                    />
                  ))}
                  <Button
                    type="button"
                    onClick={addItemTypeRow}
                    variant="secondary"
                  >
                    Add Item Type
                  </Button>
                </Col>
              )}
            </Row>

            <Row>
              {formik.values.isMultiSize && (
                <Col md="6" className="mt-4">
                  <h5>Sizes</h5>
                  {formik.values.sizes.map((item: any, index: number) => (
                    <ItemSizeRow
                      key={index}
                      index={index}
                      item={item}
                      changeHandler={(i, value, fieldName) =>
                        formik.setFieldValue(`sizes[${index}].${fieldName}`, value)
                      }
                      onRemove={(i) =>
                        formik.setFieldValue(
                          "sizes",
                          formik.values.sizes.filter((_, i) => i !== index)
                        )
                      }
                      errors={formik?.errors?.sizes?.[index]}
                    />
                  ))}
                  <Button
                    type="button"
                    onClick={addSizeRow}
                    variant="secondary"
                  >
                    Add Size
                  </Button>
                </Col>
              )}
            </Row>

            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default UpdateItem;
