import { Col, Card } from "react-bootstrap"
import { <PERSON> } from "react-router-dom"
import { DashboardCardProps } from "./dashboard.card.interface"

const DashboardCards = ({ icon, title, subtitle, description, navigate }: DashboardCardProps) => {
    return (
        <Col sm="4" className="dashboard-counts mb-3">
            <Link to={navigate}>
                <Card className=" flex-row">
                    <div className="card-icon-box d-flex justify-content-center align-items-center">
                        {icon}
                    </div>
                    <div className="d-flex flex-column gap-3">
                        <Card.Title className="mb-0">{title}</Card.Title>
                        <Card.Subtitle>{subtitle}</Card.Subtitle>
                        <Card.Text>{description}</Card.Text>
                    </div>
                </Card>
            </Link>
        </Col>
    )
}

export default DashboardCards;