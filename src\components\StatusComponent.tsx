import { FC } from "react";
import { Badge } from "react-bootstrap";
import useTranslate from "hooks/useTranslate";

const StatusComponent: FC<{ isSuspended: boolean }> = ({ isSuspended }) => {
  const { translate } = useTranslate();
  return (
    <Badge bg={isSuspended ? "danger" : "success"}>
      {isSuspended ? translate("common.inactive") : translate("common.active")}
    </Badge>
  );
};

export default StatusComponent;
