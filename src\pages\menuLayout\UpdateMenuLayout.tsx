import React, { useEffect } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { menuService } from "services";
import { MenuCategoryInterface } from "interfaces";
import { useNavigate, useParams } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { MENU_POSITIONS, MENU_TYPE } from "constants/InitialValues";

const initialValue: any = {
  menuId: 0,
  menuPosition: "",
  menuDeviceType: "",
  menuType: "",
  order: ""
};

// Helper function to create validation schema
const createValidationSchema = () =>
  yup.object().shape({
    menuId: yup.number().required("Menu is required"),
    menuDeviceType: yup.string().required("Menu device Type is required"),
    menuPosition: yup.string().when("menuDeviceType", (menuDeviceType, schema) => {
      return menuDeviceType[0] === "KIOSK"
        ? schema.required("Menu Position is required")
        : schema.notRequired();
    }),
    order: yup.number().required("Order is required").min(1, "Order must be at least 1"), // Added validation
    menuType: yup.string()
  });

// Main component
const UpdateMenuLayout: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate()
  const [title, setTitle] = React.useState("")
  const [catId, setCatId] = React.useState("")

  const { id } = useParams<{ id: string }>();

  const [initialValues, setInitialValues] = React.useState(initialValue);

  useEffect(() => {
    const fetchMenu = async () => {
      try {
        const { data: { data: { menuCategory } } } = await menuService.getMenuLayoutDetails(id!); // Fetch item data by ID
        const fetchedMenu: any = menuCategory;
        setTitle(fetchedMenu?.data?.title || "");
        setCatId(fetchedMenu?.data?.id || "");
        setInitialValues({ ...fetchedMenu, parentId: menuCategory?.parentId | 0 });
      } catch (error) {
        console.error("Error fetching item:", error);
        toast.error("Failed to fetch item data");
      }
    };

    fetchMenu();
  }, [id]);

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: createValidationSchema(),
    onSubmit: async (values) => {
      try {
        const payload: any = {
          menuId: catId,
          menuPosition: values?.menuPosition,
          menuDeviceType: values?.menuDeviceType,
          order: values?.order,
          menuType: values?.menuType
        }
        // Implement submit logic here
        await menuService.updateMenuLayoutDetails(id!, payload);
        toast.success("Category updated successfully");
        navigate('/menu-layout')
      } catch (err) {
        console.error("Error update category:", err);
        toast.error("Failed to update category");
      }
    },
  });

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isEdit: true, entityPath: "/Menu-Layout", entities: "Menu Layout" })} />
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{translate("menu.update")}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>

              <Col md="12">
                <h3>
                  {title}
                </h3>
              </Col>

              <Col md="6" className="mt-3">
                <FloatingLabel controlId="order" label="Order" className="mb-3">
                  <Form.Control
                    type="number"
                    name="order"
                    value={formik.values.order}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.order && !!formik.errors.order}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.order && typeof formik.errors.order === 'string' && formik.errors.order}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6" className="mt-3">
                <FloatingLabel controlId="menuDeviceType" label={translate("menuLayout.menuDeviceType")} className="mb-3">
                  <Form.Select
                    name="menuDeviceType"
                    value={formik.values.menuDeviceType}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.menuDeviceType && !!formik.errors.menuDeviceType}
                  >
                    <option value={0} key={0}> Select </option>
                    {
                      MENU_TYPE.map((value: string) => (
                        <option value={value} key={value}>
                          {value}
                        </option>
                      ))
                    }
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.menuDeviceType && typeof formik.errors.menuDeviceType === 'string' && formik.errors.menuDeviceType}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              {
                formik.values.menuDeviceType === "KIOSK" &&
                <Col md="6" className="mt-3">
                  <FloatingLabel controlId="menuPosition" label={translate("menuLayout.menuPosition")} className="mb-3">
                    <Form.Select
                      name="menuPosition"
                      value={formik.values.menuPosition}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      isInvalid={formik.touched.menuPosition && !!formik.errors.menuPosition}
                    >
                      <option value={0} key={0}> Select </option>
                      {MENU_POSITIONS.map((value: string) => (
                        <option value={value} key={value}>
                          {value}
                        </option>
                      ))}
                    </Form.Select>
                    <Form.Control.Feedback type="invalid">
                      {formik.touched.menuPosition && typeof formik.errors.menuPosition === 'string' && formik.errors.menuPosition}
                    </Form.Control.Feedback>
                  </FloatingLabel>
                </Col>
              }
            </Row>

            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default UpdateMenuLayout;
