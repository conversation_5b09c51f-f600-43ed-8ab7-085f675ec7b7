import { Button, Image, Nav, NavDropdown, Navbar } from "react-bootstrap";
import avatar from "assets/images/avatar.svg";
import { myAccountService } from "services";
import { Link, useNavigate } from "react-router-dom";
import { Dispatch, FC, SetStateAction, useEffect, useState } from "react";
import "styles/header.scss";
import logo from "assets/images/logo-black.svg";
import logoShort from "assets/images/logo-black-short.svg";
import { FaBars, FaLock, FaSignOutAlt, FaUser } from "react-icons/fa";
import { UserInterface } from "interfaces";
interface HeaderProps {
  isSidebarCollapsed: boolean;
  setIsSidebarCollapsed: Dispatch<SetStateAction<boolean>>;
}

const Header: FC<HeaderProps> = ({
  isSidebarCollapsed,
  setIsSidebarCollapsed,
}) => {
  const [user, setUser] = useState<UserInterface | null>(null);
  const navigate = useNavigate();
  const [expanded, setExpanded] = useState(false);
  const [btnTitle, setBtnTitle] = useState("Sync From Square");

  const handleLogout = async () => {
    try {
      const res = await myAccountService.logout();
      if (res.data.success) {
        localStorage.clear();
        setUser(null);
        navigate("/");
      }
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const res = await myAccountService.getProfile();
        setUser(res.data.data.restaurant)
      } catch (error) {
        console.error("Error fetching profile:", error);

      }
    };

    fetchProfile();
  }, []);

  const syncData = async () => {
    setBtnTitle("Syncing...");
    try {
      const _res: any = await myAccountService.syncSquareData();
    } catch (err) {
      console.log(err);
    } finally {
      setBtnTitle("Sync From Square");
    }

  }

  return (
    <div
      className={`navbar-custom ${isSidebarCollapsed ? "menu-collapsed" : ""}`}
    >
      <Navbar>
        <div className="logo-box">
          <Link to="/" className="navbar-brand">
            <img src={isSidebarCollapsed ? logoShort : logo} alt="logo" />
          </Link>
        </div>
        <ul className="list-unstyled topnav-menu  m-0">
          <li>
            <button
              className="button-menu border-0 bg-transparent"
              onClick={() => {
                setIsSidebarCollapsed(!isSidebarCollapsed);
              }}
            >
              <FaBars />
            </button>
          </li>
        </ul>
        {/* <div style={{ width: '100vw', display: 'flex', justifyContent: 'flex-end', marginRight: '6rem' }}>
          <Button onClick={syncData} size="sm">
            {btnTitle}
          </Button>
        </div> */}

        <NavDropdown
          id="nav-dropdown-dark-example"
          className="nav-link profile"
          title={
            <Image
              src={
                user?.avatar
                  ? `${process.env.REACT_APP_S3_BASE}${user?.avatar}`
                  : avatar
              }
            />
          }
        >
          <Nav.Link
            as={Link}
            to="/profile"
            className="dropdown-item justify-content-start"
            onClick={() => setExpanded(!expanded)}
          >
            <FaUser /> Profile
          </Nav.Link>

          <Nav.Link
            as={Link}
            to="/change-password"
            className="dropdown-item justify-content-start"
            onClick={() => setExpanded(!expanded)}
          >
            <FaLock /> Change Password
          </Nav.Link>
          <Nav.Link
            className="dropdown-item justify-content-start"
            onClick={handleLogout}
          >
            <FaSignOutAlt /> Logout
          </Nav.Link>
        </NavDropdown>
      </Navbar >
    </div >
  );
};

export default Header;
