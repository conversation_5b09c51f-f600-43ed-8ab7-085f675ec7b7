import axios from "axios";
import { toast } from "react-toastify";

const base = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  headers: {
    // "Content-Type": "application/json",
    "Accept-Language": "en",
    "x-split-platform": "web",
    "x-split-version": "1.0.0",
    accept: "application/json",
  },
});

base.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (token) {
    config.headers["Authorization"] = `Bearer ${token}`;
  }
  return config;
});

base.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    //clear auth token if available
    const token = localStorage.getItem("token");
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.reload();
      // window.location.href = process.env.REACT_APP_BASENAME;
    }

    if (error.message) {
      toast(error.message);
    }
    return error;
  }
);

export default base;
