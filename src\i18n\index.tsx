import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import en from "./locales/en/translation.json";
import { LanguagesInterface } from "interfaces";

const LANGUAGES: LanguagesInterface = {
  en: {
    translation: en,
  },
};

i18n.use(initReactI18next).init({
  lng: "en",
  fallbackLng: "en",
  debug: true,
  interpolation: {
    escapeValue: false,
  },
  resources: LANGUAGES,
});

export default i18n;
