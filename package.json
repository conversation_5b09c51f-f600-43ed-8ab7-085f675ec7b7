{"name": "split-admin-panel", "version": "0.1.0", "private": true, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.2.1", "@ckeditor/ckeditor5-react": "^6.2.0", "@npmcli/fs": "^3.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/i18next": "^13.0.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.87", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/react-i18next": "^8.1.0", "@types/react-tooltip": "^3.11.0", "axios": "^1.6.7", "bootstrap": "^5.3.3", "formik": "^2.4.6", "i18next": "^23.10.1", "lodash": "^4.17.21", "prop-types": "^15.8.1", "react": "^18.2.0", "react-bootstrap": "^2.10.1", "react-confirm-alert": "^3.0.6", "react-data-table-component": "^7.6.2", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-i18next": "^14.1.0", "react-icons": "^5.0.1", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-sweetalert2": "^0.6.0", "react-table": "^7.8.0", "react-toastify": "^10.0.4", "react-tooltip": "^5.28.0", "sass": "^1.71.1", "styled-components": "^6.1.8", "sweetalert2": "^11.12.4", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.17.6", "@types/react-table": "^7.7.19"}}