import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import SearchInputComponent from "components/SearchInputComponent";
import StatusComponent from "components/StatusComponent";
// import { customizationsBreadcrumbs } from "constants/breadcrums";
import useTranslate from "hooks/useTranslate";
import { CustomizationInterface } from "interfaces";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList, FaInfoCircle } from "react-icons/fa";
import { customizationService } from "services";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import DeleteActionComponent from "components/DeleteActionComponent";
import DeleteModal from "components/Modal/DeleteModal";
import { toast } from "react-toastify";
import AddActionComponent from "components/AddActionComponent";
import EditActionComponent from "components/EditActionComponent";

const Menus = () => {
  const { translate } = useTranslate()
  const [data, setData] = useState<CustomizationInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");

  const [show, setShow] = useState<any>(false);
  const [id, setId] = useState<any>("");
  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);

  const fetchData = async () => {
    setLoading(true);
    const payload = {
      page,
      pagination: true,
      skip: (page - 1) * perPage,
      limit: perPage,
      sortBy,
      sortDirection,
      searchTerm,
      activeStatus: "ALL",
    };
    try {
      const response = await customizationService.list(payload);
      setData(response.data.data.docs);
      setTotalRows(response.data.data.meta.totalDocs);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryStatus = (isSuspended: boolean) => {
    return <StatusComponent isSuspended={!isSuspended} />;
  };

  const renderActionColumn = (row: any) => {
    return (
      <>
        <EditActionComponent url={`/customization/${row.id}/edit`}></EditActionComponent>{" "}
        <DeleteActionComponent fn={() => removeRecord(Number(row.id))} />{" "}
      </>
    );
  };

  const columns = [
    {
      name: "Title",
      sortable: true,
      sortField: "title",
      selector: (row: CustomizationInterface) => row.title || "N/A",
    },
    {
      name: "Status",
      sortable: false,
      sortField: "isSuspended",
      selector: (row: CustomizationInterface) => renderCategoryStatus(!row.isSuspended),
    },
    {
      name: "Action",
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [page, perPage, sortBy, sortDirection, searchTerm]);

  const removeRecord = async (id: number | string) => {
    setId(id)
    handleShow()
  };

  const handleDelete = async (id: any) => {
    try {
      await customizationService.deleteModifier(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData => prevData.filter(staff => staff.id !== id));
    } catch (error) {
      console.error(error);
    }
    handleClose(); // Close the modal after delete
  };

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isSingle: true, entityPath: "/customizations", entities: translate("common.modifiers") })} />

      {/* <BreadcrumComponent breadcrumbs={customizationsBreadcrumbs} /> */}
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center custom-card-header-title">
              <FaList />
              <h4 className="mb-0">
                {translate("common.modifiers")}{" "}
                <FaInfoCircle
                  className="info-icon"
                  data-tooltip-id="info-tooltip" // Tooltip ID
                  data-tooltip-content={"info"} // Tooltip content
                />
                {/* Tooltip component */}
                <Tooltip
                  id="info-tooltip"
                  place="top" // Position of the tooltip
                />
              </h4>
            </div>
            <div>
              <AddActionComponent url="/customization/add" />
            </div>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex gap-3 pb-4 search-box">
            <SearchInputComponent
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          <DatatableComponent
            columns={columns}
            data={data}
            loading={loading}
            totalRows={totalRows}
            setPage={setPage}
            setPerPage={setPerPage}
            setSortBy={setSortBy}
            setSortDirection={setSortDirection}
          />

          <DeleteModal title="Customizaion" id={id} show={show} handleDelete={handleDelete} handleClose={handleClose} />
        </Card.Body>
      </Card>
    </>
  );
};

export default Menus;
