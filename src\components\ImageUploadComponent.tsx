import { FC, useRef, useState } from "react";
import { Button, Image } from "react-bootstrap";
import { FaFileUpload, FaTimes } from "react-icons/fa";
import LoadingComponent from "./LoadingComponent";
import useCurrentUser from "hooks/useCurrentUser";
import { utilService } from "services";
import "styles/img-upload.scss";
import useTranslate from "hooks/useTranslate";

const ImageUploadComponent: FC<{
  image: string;
  handleOnChange: (e: any) => void;
  setImage: (field: string, value: string) => void;
  handleRemove: () => void;
  bucketType?: "items" | "customizations" | "branches" | "products" | "menus" | "restaurants" | "staffs";
  fieldName: string;
}> = ({ image, handleOnChange, setImage, handleRemove, bucketType, fieldName }) => {
  const { loading } = useCurrentUser();
  const [preview, setPreview] = useState<string>("");
  const [isLoading, setIsLoading] = useState(loading);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { translate } = useTranslate();

  const handleFileUpload = async (e: any) => {
    const file = e.target.files[0];

    try {
      setIsLoading(true);
      const response = await utilService.getBucketUrl({
        location: bucketType || "items",
        type: "IMAGE",
        count: 1,
      });
      const webUrlData = response.data?.data?.files?.[0];
      const { filename, preview, url } = webUrlData || {};

      const requestOptions = {
        method: "PUT",
        headers: {
          "Content-Type": file.type,
        },
        body: file,
        redirect: "follow",
      };

      await utilService.uploadFile({
        url,
        requestOptions,
      });
      setPreview(preview);
      //set image value
      setImage(fieldName, filename);
    } catch (err) {
      console.log(err);
    } finally {
      setIsLoading(false);
      e.target.value = null
    }
  };

  return (
    <div className="image-upload-container rounded p-2">
      <div className="outer">
        <input
          type="file"
          id={fieldName + 'Upload'}
          accept=".png, .jpg, .jpeg"
          onChange={handleFileUpload}
          ref={fileInputRef}
          style={{ display: "none" }}
        />
        <input
          type="hidden"
          name={fieldName}
          onChange={handleOnChange}
          value={image}
        />

        {isLoading ? (
          <LoadingComponent />
        ) : (
          <>
            {preview || image ? (
              <div style={{ position: "relative", display: "inline-block" }}>
                <Image
                  src={preview || `${process.env.REACT_APP_S3_BASE}${image}`}
                />
                <Button
                  size="sm"
                  variant="danger"
                  className="remove-icon"
                  onClick={() => {
                    setPreview("");
                    handleRemove();
                  }}
                >
                  <FaTimes />
                </Button>
              </div>
            ) : (
              <button
                type="button"
                className="inner text-center"
                onClick={() =>
                  fileInputRef.current && fileInputRef.current.click()
                }
              >
                <FaFileUpload size={100} />
                <p className="mt-3">{translate("common.click-to-upload-file")}</p>
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ImageUploadComponent;
