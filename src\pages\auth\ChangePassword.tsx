import BreadcrumbComponent from "components/Breadcrumb";
import { useFormik } from "formik";
import { ReactNode, useState } from "react";
import { <PERSON><PERSON>, Card, Col, FloatingLabel, Form, Row } from "react-bootstrap";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { myAccountService } from "services";
import { ChangePasswordInterface } from "interfaces";
import * as yup from "yup";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

const ChangePassword = () => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const formik = useFormik({
    initialValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema: yup.object().shape({
      currentPassword: yup.string().required("Enter current password"),
      newPassword: yup
        .string()
        .required("Please enter a password")
        .matches(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])(?=.{8,})/,
          "Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character"
        ),
      confirmPassword: yup
        .string()
        .required("Enter confirm password")
        .oneOf(
          [yup.ref("newPassword")],
          "New and confirm passwords must match"
        ),
    }),
    onSubmit: async (values) => {
      const payload = { ...values };
      try {
        const response = await myAccountService.changePassword(payload);
        if (response.data.success) {
          toast.success(response?.data?.message);
          navigate("/dashboard");
        } else {
          toast.warning(response?.data?.message);
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  const showError = <T extends keyof ChangePasswordInterface>(field: T) =>
    formik.touched[field] && formik.errors[field] ? (
      <span className="text-danger">{formik.errors[field] as ReactNode}</span>
    ) : null;

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isSingle: true, entityPath: "/change-password", entities: "Change Password" })} />
      <Card className="custom-card">
        <Card.Header>
          <div className="custom-card-header-title">
            <h4 className="mb-0">Change Password</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form>
            <Row>
              <Col md="4">
                <FloatingLabel
                  controlId="currentPassword"
                  label="Current Password"
                  className="mb-3"
                >
                  <Form.Control
                    type={showCurrentPassword ? "text" : "password"}
                    value={formik.values.currentPassword}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  <Button
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="eye-icon"
                  >
                    {showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
                  {showError("currentPassword")}
                </FloatingLabel>
              </Col>

              <Col md="4">
                <FloatingLabel
                  controlId="newPassword"
                  label="New Password"
                  className="mb-3"
                >
                  <Form.Control
                    type={showNewPassword ? "text" : "password"}
                    value={formik.values.newPassword}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  <Button
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="eye-icon"
                  >
                    {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
                  {showError("newPassword")}
                </FloatingLabel>
              </Col>

              <Col md="4">
                <FloatingLabel
                  controlId="confirmPassword"
                  label="Confirm Password"
                  className="mb-3"
                >
                  <Form.Control
                    type={showConfirmPassword ? "text" : "password"}
                    value={formik.values.confirmPassword}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  <Button
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="eye-icon"
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
                  {showError("confirmPassword")}
                </FloatingLabel>
              </Col>

              <Col md="12" className="text-end">
                <Button
                  variant="primary"
                  type="submit"
                  onClick={(e) => {
                    e.preventDefault();
                    formik.handleSubmit();
                  }}
                >
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default ChangePassword;
