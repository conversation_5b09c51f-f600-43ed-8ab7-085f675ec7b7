import React, { useEffect, useState } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import Select from "react-select";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { promoCodeService } from "services";
import { useNavigate } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { DISCOUNT_TYPE, MENU_POSITIONS, MENU_TYPE } from "constants/InitialValues";
import { title } from "process";

const options = [
  { value: 'react', label: 'React' },
  { value: 'vue', label: 'Vue.js' },
  { value: 'angular', label: 'Angular' },
  { value: 'svelte', label: 'Svelte' },
  { value: 'nextjs', label: 'Next.js' },
  { value: 'nuxtjs', label: 'Nuxt.js' },
  { value: 'nodejs', label: 'Node.js' },
  { value: 'express', label: 'Express.js' }
];

// Helper function to create validation schema
const createValidationSchema = () =>
  yup.object().shape({

    title: yup.string().required("Title is required").min(1, "Title must be at least 1"), // Added validation
    promoCode: yup.string().required("Promo code is required").min(1, "Promo code must be at least 1"), // Added validation
    discount: yup.number().required("Discount is required"), // Added validation
    discountType: yup.string().required("Discount type is required").min(1, "Promo code must be at least 1"), // Added validation
    description: yup.string().required("Description is required").min(1, "Promo code must be at least 1"), // Added validation


  });

// Main component
const AddPromoCode: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState("a");
  const [data, setData] = useState(options)

  const initialValues = {
    endDate: new Date().toISOString().split('T')[0],
    startDate: new Date().toISOString().split('T')[0],
    description: "",
    discountType: "",
    promoCode: "",
    title: "",
    discount: 0
  };

  const formik = useFormik({
    initialValues,
    validationSchema: createValidationSchema(),
    onSubmit: async (values: any) => {
      try {
        await promoCodeService.add(values);
        toast.success("Promo code added successfully");
        navigate('/promo-code')
      } catch (err) {
        console.error("Error adding category:", err);
        toast.error("Failed to add category");
      }
    },
  });

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isAdd: true, entityPath: "/promo-code", entities: "Promo Code" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{translate("promocode.add")}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>


              <Col md="6" className="mt-3">
                <FloatingLabel controlId="title" label="Title" className="mb-3">
                  <Form.Control
                    type="text"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.title && !!formik.errors.title}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.title && typeof formik.errors.title === 'string'
                      ? formik.errors.title
                      : null}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>


              <Col md="6" className="mt-3">
                <FloatingLabel controlId="promoCode" label="PrmoCode" className="mb-3">
                  <Form.Control
                    type="text"
                    name="promoCode"
                    value={formik.values.promoCode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.promoCode && !!formik.errors.promoCode}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.promoCode && typeof formik.errors.promoCode === 'string'
                      ? formik.errors.promoCode
                      : null}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>


            </Row>

            <Row>


              <Col md="6">
                <FloatingLabel controlId="discountType" label={translate("promocode.discountType")} className="mb-3" style={{ zIndex: 0 }}>
                  <Form.Select
                    name="discountType"
                    value={formik.values.discountType}
                    // onChange={formik.handleChange}
                    onChange={(res: any) => {
                      formik.setFieldValue("discountType", res?.target?.value)
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.discountType && !!formik.errors.discountType}
                  >
                    <option value={0} key={0}> Select </option>
                    {
                      DISCOUNT_TYPE.map((value: string) => (
                        <option value={value} key={value}>
                          {value}
                        </option>
                      ))
                    }
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.discountType && typeof formik.errors.discountType === 'string'
                      ? formik.errors.discountType
                      : null}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>


              <Col md="6" className="mt-3">
                <FloatingLabel controlId="discount" label="Discount Amount" className="mb-3">
                  <Form.Control
                    type="text"
                    name="discount"
                    value={formik.values.discount}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.discount && !!formik.errors.discount}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.discount && typeof formik.errors.discount === 'string'
                      ? formik.errors.discount
                      : null}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>


            </Row>
            <Row>


              <Col md="6" className="mt-3">
                <FloatingLabel controlId="startDate" label="Start Date" className="mb-3">
                  <Form.Control
                    type="date"
                    name="startDate"
                    value={formik.values.startDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.startDate && !!formik.errors.startDate}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.startDate && typeof formik.errors.startDate === 'string'
                      ? formik.errors.startDate
                      : null}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>


              <Col md="6" className="mt-3">
                <FloatingLabel controlId="endDate" label="End Date" className="mb-3">
                  <Form.Control
                    type="date"
                    name="endDate"
                    value={formik.values.endDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.endDate && !!formik.errors.endDate}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.endDate && typeof formik.errors.endDate === 'string'
                      ? formik.errors.endDate
                      : null}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>


            </Row>
            <Row>


              <Col md="12" className="mt-3">
                <FloatingLabel controlId="description" label="Description" className="mb-3">
                  <Form.Control
                    type="text"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.description && !!formik.errors.description}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.description && typeof formik.errors.description === 'string'
                      ? formik.errors.description
                      : null}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>


            </Row>

            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default AddPromoCode;
