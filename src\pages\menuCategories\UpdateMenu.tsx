import React, { useEffect, useState } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import ImageUploadComponent from "components/ImageUploadComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { menuService } from "services";
import { MenuCategoryInterface } from "interfaces";
import { useNavigate, useParams } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { ImagePaths } from "constants/ImagePaths";
import { FaInfoCircle } from "react-icons/fa";
import { Tooltip } from "react-tooltip";

// Helper function to create validation schema
const createValidationSchema = (visitFreezer: boolean) => {
  let schema = yup.object().shape({
    title: yup
      .string()
      .trim()
      .required("Name is required")
      .min(3, "Name must be at least 3 characters")
      .max(50, "Name cannot exceed 50 characters"),
    image: yup.string().required("Image is required"),
    description: yup
      .string()
      .trim()
      .required("Description is required")
      .min(3, "Description must be at least 3 characters")
      .max(200, "Description cannot exceed 200 characters"),
    parentId: yup
      .number()
      .min(0, "Parent category must be selected or 0"),
    visitFreezer: yup.boolean(),
  });

  if (!visitFreezer) {
    schema = schema.shape({
      menuViewType: yup.string().required("Menu View Type is required"),
      productLayoutView: yup.string().required("Product Layout View is required"),
    });
  }

  return schema;
};

const useMenuData = (id: any) => {
  const [data, setData] = useState<{
    menuCategories: MenuCategoryInterface[];
  }>({
    menuCategories: [],
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [menus] = await Promise.all([
          menuService.getParentMenuCategories({
            page: 1,
            pagination: false,
            activeStatus: "ALL",
            isParent: true,
          }),
        ]);
        const data: any = menus.data.data.menuCategory.filter((res: any) => res?.id !== Number(id))
        setData({
          menuCategories: data || [],
        });
        console.log()
      } catch (error) {
        console.error(error);
        toast.error("Failed to load data");
      }
    };

    fetchData();
  }, []);

  return data;
};

const UpdateMenu: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { menuCategories } = useMenuData(id);

  const [initialValues, setInitialValues] = useState({
    title: "",
    image: "",
    description: "",
    parentId: 0,
    visitFreezer: false,
    isFeatured: false,
    menuViewType: "SELF_VIEW",
    productLayoutView: "LAYOUT_ONE",
  });

  useEffect(() => {
    const fetchMenu = async () => {
      try {
        const {
          data: {
            data: { menuCategory },
          },
        } = await menuService.getDetail(id!);

        const fetchedMenu = menuCategory as any;

        setInitialValues({
          ...fetchedMenu,
          parentId: fetchedMenu?.parentId || 0,
          visitFreezer: fetchedMenu.visitFreezer || false,
          isFeatured: fetchedMenu.isFeatured || false,
          menuViewType: fetchedMenu.menuViewType || "SELF_VIEW",
          productLayoutView: fetchedMenu.productLayoutView || "LAYOUT_ONE",
        });
      } catch (error) {
        console.error("Error fetching item:", error);
        toast.error("Failed to fetch item data");
      }
    };

    fetchMenu();
  }, [id]);

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: createValidationSchema(initialValues.visitFreezer),
    onSubmit: async (val) => {
      const values: any = val.visitFreezer
        ? (({ menuViewType, productLayoutView, ...rest }) => rest)(val)
        : val;

      try {
        const filteredValues = {
          ...Object.fromEntries(
            Object.entries(values).filter(([key]) => key !== "parentTitle")
          ),
          parentId: values.parentId ? Number(values.parentId) : null,
        };

        await menuService.update(id!, filteredValues as unknown as MenuCategoryInterface);
        toast.success("Category updated successfully");
        navigate("/categories");
      } catch (err) {
        console.error("Error update category:", err);
        toast.error("Failed to update category");
      }
    },
  });

  const isVisitFreezer = formik.values.visitFreezer;

  return (
    <>
      <BreadcrumbComponent
        breadcrumbs={getBreadcrumbs({
          isEdit: true,
          entityPath: "/categories",
          entities: "Categories",
        })}
      />
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{translate("menu.edit")}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <FloatingLabel
                  controlId="title"
                  label={translate("menu.title")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.title && !!formik.errors.title}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.errors.title}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="parentId"
                  label={translate("menu.selectMenuCategories")}
                  className="mb-3"
                >
                  <Form.Select
                    name="parentId"
                    value={formik.values.parentId}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.parentId && !!formik.errors.parentId}
                  >
                    <option value={0}>Select</option>
                    {menuCategories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.title}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.errors.parentId}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="3" style={{ display: 'flex' }}>
                <Form.Check
                  type="switch"
                  id="layering-switch"
                  label="Visit Freezer"
                  checked={formik.values.visitFreezer}
                  onChange={(e) => {
                    formik.setFieldValue("visitFreezer", e.target.checked);
                  }}
                />
                <FaInfoCircle
                  data-tooltip-id="visitFreezer"
                  data-tooltip-content="This Category Items Available When Order From Restaurant"
                  className="ms-2"
                />
                <Tooltip id="visitFreezer" />
              </Col>


              <Col md="3" style={{ display: 'flex' }}>
                <Form.Check
                  type="switch"
                  id="isFeatured-switch"
                  label="Featured"
                  checked={formik.values.isFeatured}
                  onChange={(e) => {
                    formik.setFieldValue("isFeatured", e.target.checked);
                  }}
                />
                <FaInfoCircle
                  data-tooltip-id="isFeatured"
                  data-tooltip-content="isFeatured"
                  className="ms-2"
                />
                <Tooltip id="isFeatured" />
              </Col>

              {!isVisitFreezer && (
                <Col md="6">
                  <Form.Group className="mb-3">
                    <Form.Label>{translate("menu.menuViewType")}</Form.Label>
                    <div className="d-flex gap-3 align-items-center">
                      <Form.Check
                        type="radio"
                        name="menuViewType"
                        id="self-view"
                        label="Self View"
                        value="SELF_VIEW"
                        checked={formik.values.menuViewType === "SELF_VIEW"}
                        onChange={() => formik.setFieldValue("menuViewType", "SELF_VIEW")}
                      />
                      <FaInfoCircle
                        data-tooltip-id="selfViewTip"
                        data-tooltip-content="Redirects to the category detail page."
                        className="ms-2"
                      />
                      <Tooltip id="selfViewTip" />
                      <Form.Check
                        type="radio"
                        name="menuViewType"
                        id="parent-view"
                        label="Parent View"
                        value="PARENT_VIEW"
                        checked={formik.values.menuViewType === "PARENT_VIEW"}
                        onChange={() => formik.setFieldValue("menuViewType", "PARENT_VIEW")}
                      />
                      <FaInfoCircle
                        data-tooltip-id="parentViewTip"
                        data-tooltip-content="Redirects to the parent category detail page."
                        className="ms-2"
                      />
                      <Tooltip id="parentViewTip" />
                    </div>
                  </Form.Group>
                </Col>
              )}

              {!isVisitFreezer && formik.values.menuViewType === "PARENT_VIEW" && (
                <Col md="12">
                  <Form.Group className="mb-3">
                    <Form.Label>{translate("menu.productLayoutView")}</Form.Label>
                    <div className="d-flex gap-4 align-items-center">
                      <Form.Check
                        type="radio"
                        name="productLayoutView"
                        id="layout-one"
                        value="LAYOUT_ONE"
                        checked={formik.values.productLayoutView === "LAYOUT_ONE"}
                        onChange={() => formik.setFieldValue("productLayoutView", "LAYOUT_ONE")}
                        label={
                          <img
                            src={ImagePaths.layout_one}
                            alt="Layout One"
                            style={{
                              width: "100%",
                              maxWidth: "500px",
                              height: "400px",
                              cursor: "pointer",
                              border:
                                formik.values.productLayoutView === "LAYOUT_ONE"
                                  ? "2px solid black"
                                  : "2px dashed black",
                              objectFit: "contain",
                            }}
                          />
                        }
                      />
                      <Form.Check
                        type="radio"
                        name="productLayoutView"
                        id="layout-two"
                        value="LAYOUT_TWO"
                        checked={formik.values.productLayoutView === "LAYOUT_TWO"}
                        onChange={() => formik.setFieldValue("productLayoutView", "LAYOUT_TWO")}
                        label={
                          <img
                            src={ImagePaths.layout_two}
                            alt="Layout Two"
                            style={{
                              width: "100%",
                              maxWidth: "500px",
                              height: "400px",
                              cursor: "pointer",
                              border:
                                formik.values.productLayoutView === "LAYOUT_TWO"
                                  ? "2px solid black"
                                  : "2px dashed black",
                              objectFit: "contain",
                            }}
                          />
                        }
                      />
                    </div>
                  </Form.Group>
                </Col>
              )}
            </Row>

            <Row>
              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("menu.upload-image")} (335px x 265px)</Form.Label>
                  <ImageUploadComponent
                    bucketType="menus"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.image}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("image", "")}
                    fieldName="image"
                  />
                  {formik.touched.image && formik.errors.image && (
                    <small className="text-danger">{formik.errors.image}</small>
                  )}
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default UpdateMenu;
