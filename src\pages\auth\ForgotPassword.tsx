import logo from "assets/images/logo.png";
import { useFormik } from "formik";
import { ForgotPasswordInterface } from "interfaces";
import { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Container,
  FloatingLabel,
  Form,
  Image,
  Row,
} from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { authService } from "services";
import * as yup from "yup";

const ForgotPassword = () => {
  const navigate = useNavigate();

  const formik = useFormik({
    initialValues: {
      email: "",
      type: "FORGOT_PASSWORD",
      verificationType: "EMAIL"
    },
    validationSchema: yup.object().shape({
      email: yup
        .string()
        .email("Please enter valid email")
        .required("Please enter email"),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      const payload = { ...values };
      try {
        const response = await authService.requestOtp(payload);
        setSubmitting(false);
        if (response.data.success) {
          toast.success(response?.data?.message);
          navigate(`/verify-otp?email=${payload.email}&type=${payload.type}&verificationType=${payload.verificationType}`)
        } else {
          toast.warning(response?.data?.message);
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  const showError = <T extends keyof ForgotPasswordInterface>(field: T) =>
    formik.touched[field] && formik.errors[field] ? (
      <span className="text-danger">{formik.errors[field] as ReactNode}</span>
    ) : null;

  return (
    <Container>
      <Row>
        <div
          className="card-box d-flex justify-content-center align-items-center "
          style={{ minHeight: "100vh" }}
        >
          <Card className="bg-transparent">
            <Card.Body>
              <div className="text-center">
                <Image src={logo} alt="logo" className="card-box-logo" />
              </div>
              <Card.Title className="text-center mb-3">
                Forgot Password?
              </Card.Title>
              <Card.Subtitle className="text-center mb-3">
                Enter your email address
              </Card.Subtitle>
              <Form>
                <FloatingLabel controlId="email" label="Email" className="mb-3">
                  <Form.Control
                    type="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("email")}
                </FloatingLabel>

                <Button
                  variant="primary"
                  type="submit"
                  onClick={(e) => {
                    e.preventDefault();
                    formik.handleSubmit();
                  }}
                  className="mb-3 card-box-btn"
                  disabled={formik.isSubmitting}
                >
                  {formik.isSubmitting ? "Submitting..." : "Submit"}
                </Button>
                <div className="text-center">
                  <Link to="/">Back to login</Link>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </div>
      </Row>
    </Container>
  );
};

export default ForgotPassword;
