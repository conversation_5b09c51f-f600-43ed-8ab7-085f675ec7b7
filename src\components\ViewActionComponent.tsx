import { <PERSON> } from "react";
import { But<PERSON> } from "react-bootstrap";
import { FaEye } from "react-icons/fa";
import { Link } from "react-router-dom";

const ViewActionComponent: FC<{ url: string }> = ({ url }) => {
  return (
    <Link to={url}>
      <Button size="sm" variant="primary">
        <FaEye title="View" />
      </Button>
    </Link>
  );
};

export default ViewActionComponent;
