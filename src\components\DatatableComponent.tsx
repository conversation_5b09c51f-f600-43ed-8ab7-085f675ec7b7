import { FC } from "react";
import DataTable, { createTheme } from "react-data-table-component";
import LoadingComponent from "./LoadingComponent";
import NoDataComponent from "./NoDataComponent";

interface DataTableInterface {
  columns: any[];
  data: any[];
  loading: boolean;
  totalRows: number;
  setPage: (page: number) => void;
  setPerPage: (perPage: number) => void;
  setSortBy: (sortField: string) => void;
  setSortDirection: (sortDirection: string) => void;
}
const DatatableComponent: FC<DataTableInterface> = ({
  columns,
  data,
  loading,
  totalRows,
  setPage,
  setPerPage,
  setSortBy,
  setSortDirection,
}) => {

  const onSort = async (column: any, sortDirection: string) => {
    setSortBy(column.sortField);
    setSortDirection(sortDirection.toUpperCase());
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      progressPending={loading}
      progressComponent={LoadingComponent()}
      pagination
      paginationServer
      paginationTotalRows={totalRows}
      onChangeRowsPerPage={(page) => setPerPage(page)}
      onChangePage={(page) => setPage(page)}
      noDataComponent={NoDataComponent()}
      onSort={onSort}
      sortServer
      responsive={true}
    />
  );
};

export default DatatableComponent;
