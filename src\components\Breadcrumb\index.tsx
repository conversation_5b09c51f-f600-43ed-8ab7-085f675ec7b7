import { BreadcrumbsInterface } from "interfaces";
import { FC } from "react";
import { Breadcrumb } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";

const BreadcrumbComponent: FC<BreadcrumbsInterface> = ({ breadcrumbs }) => {
  return (
    <div className="custom-breadcrumb">
      <Breadcrumb>
        {breadcrumbs.map((breadcrumb) =>
          breadcrumb.isActive ? (
            <Breadcrumb.Item active key={breadcrumb.title}>
              {breadcrumb.title}
            </Breadcrumb.Item>
          ) : (
            <Link
              to={breadcrumb.link}
              className="breadcrumb-item"
              key={breadcrumb.title}
            >
              {breadcrumb.title}
            </Link>
          )
        )}
      </Breadcrumb>
    </div>
  );
};

export default BreadcrumbComponent;
