import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Card, Button, ListGroup } from "react-bootstrap";
import { FaEdit } from "react-icons/fa";
import { customerService } from "services";
import { CustomerInterface } from "interfaces";
import StatusComponent from "components/StatusComponent";
import ImageModal from "components/ImageModal";
import useTranslate from "hooks/useTranslate";
import BreadcrumbComponent from "components/Breadcrumb";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
// import { customersDetailBreadcrumbs } from "constants/breadcrums";

const CustomerDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { translate } = useTranslate();
  const [staff, setStaff] = useState<CustomerInterface | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchStaffDetails = async () => {
      setLoading(true);
      try {
        const response = await customerService.getDetail(Number(id));
        setStaff(response.data.data.user);
      } catch (error) {
        console.error("Error fetching staff details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStaffDetails();
  }, [id]);

  if (loading) {
    return <div className="text-center">{translate("common.loading")}</div>;
  }

  if (!staff) {
    return <div className="text-center">{translate("common.notFound")}</div>;
  }

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isDetail: true, entityPath: "/customers", entities: "Customers" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">{translate("staff.details")}</h4>
            {/* <Button
              variant="primary"
              onClick={() => navigate(`/customers/${id}/edit`)}
            >
              <FaEdit /> {translate("common.edit")}
            </Button> */}
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex flex-column align-items-start">
            <div className="mb-3">
              <p><span style={{ fontWeight: '800' }}>{translate("staff.name")}</span> : {staff.firstName + ' ' + staff.lastName || "N/A"}</p>
            </div>
            <div className="mb-3">
              <p><span style={{ fontWeight: '800' }}>{translate("staff.email")}</span> : {staff.email || "N/A"}</p>
            </div>
            <div className="mb-3">
              <p><span style={{ fontWeight: '800' }}>{translate("staff.phone")}</span> : {staff.formattedPhone || "N/A"}</p>
            </div>
            <div className="mb-3 d-flex">
              <p><span style={{ fontWeight: '800' }}>{translate("common.status")}</span> : <StatusComponent isSuspended={!!staff.isSuspended} /> </p>
            </div>
            <div className="mb-3 d-flex">
              <p><span style={{ fontWeight: '800' }}>{translate("product.averageOrderValue")}</span> : {staff?.averageOrderValue}</p>
            </div>
            <div className="mb-3 d-flex">
              <p><span style={{ fontWeight: '800' }}>{translate("product.mostOrderProduct")}</span> : {staff?.mostOrderProduct}</p>
            </div>
            <div className="mb-3">
              <p><strong>{translate("staff.image")}:</strong></p>
              <ImageModal
                height={"150px"}
                thumbnailSrc={staff.avatar}
                altText={staff.firstName}
              />
            </div>

          </div>
        </Card.Body>
      </Card>
    </>
  );
};

export default CustomerDetail;
