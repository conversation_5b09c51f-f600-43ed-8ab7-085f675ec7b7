import logo from "assets/images/logo.png";
import { useFormik } from "formik";
import { ResetPasswordInterface } from "interfaces";
import { ReactNode, useState } from "react";
import {
  <PERSON>ton,
  Card,
  Container,
  FloatingLabel,
  Form,
  Image,
  Row,
} from "react-bootstrap";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { authService } from "services";
import * as yup from "yup";

const ResetPassword = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const email = searchParams.get("email");

  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const formik = useFormik({
    initialValues: {
      email,
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema: yup.object().shape({
      newPassword: yup
        .string()
        .required("Please enter a password")
        .matches(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])(?=.{8,})/,
          "Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character"
        ),
      confirmPassword: yup
        .string()
        .required("Enter confirm password")
        .oneOf(
          [yup.ref("newPassword")],
          "New and confirm passwords must match"
        ),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      const payload = { email: values.email, password: values.newPassword }
      try {
        const response = await authService.resetPassword(payload);
        setSubmitting(false);
        if (response.data.success) {
          toast.success(response?.data?.message);
          navigate("/");
        } else {
          toast.warning(response?.data?.message);
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  const showError = <T extends keyof ResetPasswordInterface>(field: T) =>
    formik.touched[field] && formik.errors[field] ? (
      <span className="text-danger">{formik.errors[field] as ReactNode}</span>
    ) : null;

  return (
    <Container>
      <Row>
        <div
          className="card-box d-flex justify-content-center align-items-center "
          style={{ minHeight: "100vh" }}
        >
          <Card className="bg-transparent">
            <Card.Body>
              <div className="text-center">
                <Image src={logo} alt="logo" className="card-box-logo" />
              </div>
              <Card.Title className="text-center mb-3">
                Reset Password
              </Card.Title>
              <Card.Subtitle className="text-center mb-3">
                Enter your new password
              </Card.Subtitle>
              <Form>

                <FloatingLabel
                  controlId="newPassword"
                  label="New Password"
                  className="mb-3"
                >
                  <Form.Control
                    type={showNewPassword ? "text" : "password"}
                    value={formik.values.newPassword}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  <Button
                    className="eye-icon"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
                  {showError("newPassword")}
                </FloatingLabel>

                <FloatingLabel
                  controlId="confirmPassword"
                  label="Confirm Password"
                  className="mb-3"
                >
                  <Form.Control
                    type={showConfirmPassword ? "text" : "password"}
                    value={formik.values.confirmPassword}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  <Button
                    className="eye-icon"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
                  {showError("confirmPassword")}
                </FloatingLabel>

                <Button
                  variant="primary"
                  type="submit"
                  onClick={(e) => {
                    e.preventDefault();
                    formik.handleSubmit();
                  }}
                  className="mb-3 card-box-btn"
                  disabled={formik.isSubmitting}
                >
                  {formik.isSubmitting ? "Resetting..." : "Reset"}
                </Button>
                <div className="text-center">
                  <Link to="/">Back to login</Link>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </div>
      </Row>
    </Container>
  );
};

export default ResetPassword;
