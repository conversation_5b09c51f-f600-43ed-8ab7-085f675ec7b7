import React, { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';

// Define types for the features and modules
export interface Feature {
    id: number;
    title: string;
    description: string;
    slug: string;
}

export interface Module {
    id: number;
    title: string;
    description: string;
    slug: string;
    isSuspended: boolean;
    features: Feature[];
}


interface PermissionProps {
    permissionsList: Module[] | any,
    selectedPermissions?: string[];
    onChange: (selectedPermissions: string[]) => void; // Prop to handle changes
}



const PermissionsComponent = ({ permissionsList, selectedPermissions: initialSelectedPermissions = [], onChange }: PermissionProps) => {
    const permissionsData = permissionsList;
    const [selectedPermissions, setSelectedPermissions] = useState<string[]>(initialSelectedPermissions);

    useEffect(() => {
        if (initialSelectedPermissions.length > 0) {
            setSelectedPermissions(initialSelectedPermissions);
        }
    }, [initialSelectedPermissions]);

    const handleCheckboxChange = (slug: string) => {
        setSelectedPermissions((prev) => {
            const newSelected = prev.includes(slug)
                ? prev.filter((permission) => permission !== slug)
                : [...prev, slug];

            onChange(newSelected);
            return newSelected;
        });
    };

    return (
        <div>
            <h2>Permissions</h2>
            {Array.isArray(permissionsData) && permissionsData?.map((module: Module) => (
                <div key={module.id} style={{ marginBottom: '20px' }}>
                    <h4>{module.title}</h4>
                    {/* <p>{module.description}</p> */}
                    <Row >
                        {module.features.map((feature: Feature) => (
                            <Col lg={3} key={feature.id}>
                                <input
                                    type="checkbox"
                                    id={feature.slug}
                                    checked={selectedPermissions.includes(feature.slug)}
                                    onChange={() => handleCheckboxChange(feature.slug)}
                                />
                                <label htmlFor={feature.slug} style={{ marginLeft: '8px' }}>
                                    {feature.title}
                                </label>
                                {/* <p style={{ marginBottom: 0, marginLeft: '20px' }}>
                                    {feature.description}
                                </p> */}
                            </Col>
                        ))
                        }
                    </Row>
                </div>
            ))}

        </div>
    );
};

export default PermissionsComponent;
