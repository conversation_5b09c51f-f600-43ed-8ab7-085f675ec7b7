.sidebar-custom {
  width: 240px;
  background: #fff;
  bottom: 0;
  padding: 20px 0;
  position: fixed;
  overflow: auto;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
  top: 70px;
  -webkit-box-shadow: 0 0 35px 0 rgba(154, 161, 171, 0.15);
  box-shadow: 0 0 35px 0 rgba(154, 161, 171, 0.15);
  &.collapsed {
    position: absolute;
    padding-top: 0;
    width: 70px !important;
    z-index: 5;
    ul {
      li {
        a {
          text-align: center;
          svg {
            font-size: 25px;
            margin: 0px;
          }
          span {
            display: none;
          }
        }
      }
    }
  }

  ul {
    padding: 0;
    li {
      list-style: none;
      font-size: 0.9375rem;
      font-weight: 400;
      line-height: normal;
      border-bottom: 1px solid #fff5f5;
      a {
        color: #000;
        display: block;
        position: relative;
        -webkit-transition: all 0.4s;
        transition: all 0.4s;
        font-size: 15px;
        font-weight: 600;
        line-height: 1.5rem;
        padding: 0.75rem 1rem;
        text-decoration: none;

        &:hover {
          background: #f2f2f2;
        }
        &.active {
          background: #f2f2f2;
        }

        svg {
          display: inline-block;
          line-height: 1.0625rem;
          margin: 0 10px 0 3px;
          text-align: center;
          vertical-align: middle;
          font-size: 20px;
          fill: #000;
        }
        span {
          vertical-align: middle;
          &.menu-arrow {
            float: right;
            svg {
              margin: 0px;
            }
          }
        }
      }
      ul {
        &.collapsed {
          display: none;
          &.in {
            display: block;
            padding-left: 10px;
          }
        }
        li {
          border-bottom: none;
          a {
            font-size: 14px;
          }
        }
      }
    }
  }
}
@media (max-width: 767px) {
  .sidebar-custom {
    display: none;
    &.collapsed {
      display: block;
      width: 240px !important;
      ul {
        li {
          a {
            text-align: left;
            svg {
              font-size: 20px;
              margin-right: 10px;
            }
            span {
              display: inline-block;
            }
          }
        }
      }
    }
  }
}
