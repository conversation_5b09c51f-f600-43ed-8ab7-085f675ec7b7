import base from "./base.service";
const requestOtp = (payload) => base.post("/auth/request-otp", payload);
const verifyOtp = (payload) => base.post("/auth/verify-otp", payload);
const login = (payload) => base.post("/auth/login", payload);
const resetPassword = (payload) => base.post("/auth/reset-password", payload);

export const authService = {
  requestOtp,
  verifyOtp,
  login,
  resetPassword,
};
