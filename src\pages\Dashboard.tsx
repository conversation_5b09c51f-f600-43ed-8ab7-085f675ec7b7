import { useCallback, useEffect, useMemo, useState } from "react";
import { Row } from "react-bootstrap";
import { FaFirstOrder, FaLocation<PERSON>rrow, FaMoneyBill, FaUsers } from "react-icons/fa";
import BreadcrumbComponent from "components/Breadcrumb";
import { MetricesInterface } from "interfaces";
import { dashboardServices } from "services";
import DashboardCards from "./dashboard/DashboardCard";
import { DashboardCardProps } from "./dashboard/dashboard.card.interface";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

const Dashboard = () => {
  const [data, setData] = useState<any>(null);

  const fetchData = useCallback(() => {
    dashboardServices
      .getDashBoardDetails()
      .then((res: any) => setData(res?.data?.data?.dashboard))
      .catch((error) => {
        console.error("Error fetching dashboard details:", error);
      });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Destructuring `data` to improve readability
  const {
    totalOrder,
    orderFromKiosk,
    orderFromApp,
    totalTransaction,
    totalLocationKiosk,
    totalKiosk,
    ongoingOrders,
    pendingOrder,
  } = data || {};

  // Array of card data to generate cards dynamically
  const cardData = useMemo(
    () => [
      {
        title: totalOrder,
        subtitle: "Total orders",
        description: "Number of orders received.",
        icon: <FaFirstOrder />,
        navigate: "#",
      },
      {
        title: orderFromKiosk,
        subtitle: "Total orders from self-ordering kiosk",
        description: "Number of orders from self-ordering kiosk.",
        icon: <FaFirstOrder />,
        navigate: "#",
      },
      {
        title: orderFromApp,
        subtitle: "Total orders from mobile app",
        description: "Number of orders received from mobile app",
        icon: <FaFirstOrder />,
        navigate: "#",
      },
      {
        title: `$${totalTransaction?.toFixed(2)}`,
        subtitle: "Total Amount Received",
        description: "Total Amount Received.",
        icon: <FaMoneyBill />,
        navigate: "#",
      },
      {
        title: totalLocationKiosk,
        subtitle: "No. of Locations with kiosk installation",
        description: "No. of Locations with kiosk installation.",
        icon: <FaLocationArrow />,
        navigate: "#",
      },
      {
        title: totalKiosk,
        subtitle: "No. of kiosk installations",
        description: "No. of kiosk installations.",
        icon: <FaUsers />,
        navigate: "#",
      },
      {
        title: ongoingOrders,
        subtitle: "Ongoing Orders",
        description: "Number of orders ongoing.",
        icon: <FaUsers />,
        navigate: "#",
      },
      {
        title: pendingOrder,
        subtitle: "Pending Orders",
        description: "Number of orders pending.",
        icon: <FaUsers />,
        navigate: "#",
      },
    ],
    [
      totalOrder,
      orderFromKiosk,
      orderFromApp,
      totalTransaction,
      totalLocationKiosk,
      totalKiosk,
      ongoingOrders,
      pendingOrder,
    ]
  );

  const dashboardCards = useMemo(
    () => (
      <Row>
        <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isSingle: true, entityPath: "/dashboard", entities: "" })} />
        {cardData.map((config: DashboardCardProps, index: number) => (
          <DashboardCards
            key={`dashboard_${index + 10}`}
            title={config.title}
            subtitle={config.subtitle}
            description={config.description}
            navigate={config.navigate}
            icon={config.icon}
          />
        ))}
      </Row>
    ),
    [cardData]
  );

  return <>{dashboardCards}</>;
};

export default Dashboard;
