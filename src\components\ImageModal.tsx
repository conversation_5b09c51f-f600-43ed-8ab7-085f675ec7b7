// src/ImageModal.js
import React, { useState } from 'react';
import { Modal, Button } from 'react-bootstrap';

const ImageModal = ({ thumbnailSrc, altText, height = "150px",
    width = "150px" }: {
        thumbnailSrc: string, altText: string; height?: string;
        width?: string;
    }) => {
    const [show, setShow] = useState(false);

    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);
    const s3BucketUrl = process.env.REACT_APP_S3_BASE
    const fullImageSrc = `${s3BucketUrl}${thumbnailSrc}`
    return (
        thumbnailSrc ?
            <>
                {/* Thumbnail Image */}
                <img
                    src={fullImageSrc}
                    alt={altText}
                    className="img-thumbnail"
                    style={{ cursor: 'pointer' }}
                    onClick={handleShow}
                    height={height}
                    width={width}
                />

                {/* Modal */}
                <Modal show={show} onHide={handleClose} centered>
                    <Modal.Header closeButton></Modal.Header>
                    <Modal.Body>
                        <img
                            src={fullImageSrc}
                            alt={altText}
                            className="img-fluid"
                        />
                    </Modal.Body>
                </Modal>
            </>
            : <></>
    );
};

export default ImageModal;
