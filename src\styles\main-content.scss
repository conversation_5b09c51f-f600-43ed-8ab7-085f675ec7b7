$primary: #000;
.main-content {
  background: #f2f2f2;
  margin-left: 240px;
  overflow: hidden;
  padding: 20px;
  min-height: 91vh;
  margin-top: 70px;
  &.menu-collapsed {
    margin-left: 70px;
  }
  .dashboard-counts {
    .card {
      padding: 24px;
      border-radius: 20px;
      gap: 16px;
      &-icon-box {
        background: #000;
        padding: 10px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        svg {
          font-size: 30px;
          fill: #fff;
        }
      }
    }
  }
  .custom-card {
    border: none;
    padding: 24px;
    .card-header {
      background: transparent;
      padding: 0px;
      border-bottom: 0;
    }
    &-header-title {
      svg {
        fill: #000;
      }
      h4 {
        font-size: 20px;
      }
    }
    .card-body {
      padding: 0px;
      padding-top: 30px;
      .search-box {
        max-width: 400px;
        margin-left: auto;
        &-btn {
          background: $primary;
          border: 0px;
          color: #000;
          font-weight: 600;
          padding: 8px 20px;
          font-size: 16px;
        }
      }
    }
    .user-img {
      max-width: 400px;
      border-radius: 20px;
      height: 250px;
      object-fit: cover;
    }
    .form-floating > .form-control:not(:placeholder-shown) ~ label::after {
      background-color: transparent;
    }
  }
}
@media (max-width: 767px) {
  .main-content {
    margin-left: 0px;
  }
}
