import AddActionComponent from "components/AddActionComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import SearchInputComponent from "components/SearchInputComponent";
import StatusActionComponent from "components/StatusActionComponent";
import StatusComponent from "components/StatusComponent";
import ViewActionComponent from "components/ViewActionComponent";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
// import { menusBreadcrumbs } from "constants/breadcrums";
import { MenuCategoryInterface } from "interfaces";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { menuService } from "services";
import { toast } from "react-toastify";
import DeleteActionComponent from "components/DeleteActionComponent";
import DeleteModal from "components/Modal/DeleteModal";

const Menus = () => {
  const [data, setData] = useState<MenuCategoryInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");
  const [show, setShow] = useState<any>(false);
  const [id, setId] = useState<any>("");
  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);


  const fetchData = async () => {
    setLoading(true);
    const payload = {
      page,
      pagination: true,
      skip: (page - 1) * perPage,
      limit: perPage,
      sortBy,
      sortDirection,
      searchTerm,
      activeStatus: "ALL",
      isParent: true
    };
    try {
      const response = await menuService.getMenuCategories(payload);
      setData(response.data.data.docs);
      setTotalRows(response.data.data.meta.totalDocs);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (id: number | string) => {
    try {
      await menuService.updateCategoryStatus(id);
      setData(prevData =>
        prevData.map(category =>
          category.id === id ? { ...category, isSuspended: !category.isSuspended } : category
        )
      );
    } catch (error) {
      console.error(error);
    }
  };

  //   const removeMenuCategory = (id: number | string) => {
  //     console.log({ test: id });
  //   };

  const handleDelete = async (id: any) => {
    try {
      await menuService.deleteCategory(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData => prevData.filter(staff => staff.id !== id));
    } catch (error) {
      console.error(error);
    }
    handleClose(); // Close the modal after delete
  };


  const removeRecord = async (id: number | string) => {
    setId(id)
    handleShow()

  };


  const renderCategoryStatus = (isSuspended: boolean) => {
    return <StatusComponent isSuspended={!isSuspended} />;
  };

  const renderActionColumn = (row: MenuCategoryInterface) => {
    return (
      <>
        <StatusActionComponent
          id={row.id}
          isSuspended={row.isSuspended}
          updateStatus={() => updateStatus(row.id)}
        />{" "}

        <EditActionComponent url={`/categories/${row.id}/edit`}></EditActionComponent>{" "}

        <ViewActionComponent url={`/categories/${row.id}/view`} />{" "}
        <DeleteActionComponent fn={() => removeRecord(Number(row.id))} />{" "}
      </>
    );
  };

  const columns = [
    {
      name: "Title",
      sortable: true,
      sortField: "title",
      selector: (row: MenuCategoryInterface) => row.title || "N/A",
    },
    {
      name: "Status",
      sortable: false,
      sortField: "email",
      selector: (row: MenuCategoryInterface) => renderCategoryStatus(!row.isSuspended),
    },
    {
      name: "Action",
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [page, perPage, sortBy, sortDirection, searchTerm]);

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/categories", entities: "Category Listing" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center custom-card-header-title">
              <FaList />
              <h4 className="mb-0">Menus</h4>
            </div>
            <div>
              <AddActionComponent url="/categories/add" />
            </div>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex gap-3 pb-4 search-box">
            <SearchInputComponent
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          <DatatableComponent
            columns={columns}
            data={data}
            loading={loading}
            totalRows={totalRows}
            setPage={setPage}
            setPerPage={setPerPage}
            setSortBy={setSortBy}
            setSortDirection={setSortDirection}
          />

          <DeleteModal title="Category" id={id} show={show} handleDelete={handleDelete} handleClose={handleClose} />
        </Card.Body>
      </Card>
    </>
  );
};

export default Menus;
