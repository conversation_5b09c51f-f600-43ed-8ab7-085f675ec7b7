{"header": {"dashboard": "Dashboard", "viewProfile": "View Profile", "settings": "Settings", "logout": "Logout", "signup": "Signup", "signin": "Signin"}, "auth": {"welcomeBack": "Welcome back", "email": "Email"}, "footer": {"rightsReserved": "All rights reserved."}, "validation": {"required": "{{field}} is required.", "min": "{{field}} must be at least {{number}} characters.", "max": "{{field}} must be less than {{number}} characters.", "password": {"match": "{{field}} must match."}, "email": "Email must be a valid email", "url": "{{field}} must be a valid URL", "agreeTerms": "You must agree before submitting.", "uploadAValidImage": "Please upload a valid image.", "imageSize5MB": "Image size should be less then 5 MB.", "pleaseTryAgain": "Please Try Again"}, "404Page": {"pageNotFound": "Oops! Page Not Found", "pageNotExist": "The page you are looking for might be under construction or does not exist.", "goToHome": "Go to Home"}, "dashboard": {"customizations": "Customizations"}, "items": {"items": "Items", "title": "Title", "edit": "Edit", "details": "Details", "description": "Description", "price": "Price", "active": "Active", "image": "Image", "sizes": "Sizes", "noSizes": "No Sizes are available", "noTypes": "No Types are available", "types": "Types"}, "product": {"page-title": "Products", "title": "Title", "image": "Image", "active": "Status", "action": "Action", "add": "Add Product", "type": "Type", "presets": "Presets", "createown": "Create Own", "productType": "Product Type", "sweet": "Sweet", "spicy": "SPICY", "description": "Description", "price": "Price", "isCustomizable": "Is Customizable", "isTreatYourSelf": "Is Treat YourSelf", "isSuggested": "Is Suggested", "upload-image": "Upload Image", "background-image": "Background Image", "addCustomization": "Add Customizations", "isRequired": "Is Required", "selectSuggestionProduct": "Select Suggestion Product", "selectMenuCategories": "Select Category", "selectMenuSubCategories": "Select Sub Category", "selectCustomizations": "Select Modifiers", "pleaseSelectCustomizations": "Please Select Modifiers", "maxQuantity": "Max Free Items", "details": "Details", "customizations": "Customizations", "noItems": "No Items", "noCustomizations": "No Customizations", "menuCategories": "Category", "subMenuCategories": "SubCategory", "suggestionProducts": "Suggestion Product", "customizationLabel": "Customization", "itemLabel": "<PERSON><PERSON>", "isAdded": "Is Added", "items": "Items", "edit": "Edit", "mostOrderProduct": "Most Order Product", "averageOrderValue": "Average Order Value", "desc": "Description", "read": "Read", "requiredModifier": "Required Modifier", "maxModifiers": "Maximum Modifiers", "needAssist": "Need Assistance ?", "needAssistInfo": "Restaurant will assist you!", "suggestedInfo": "it allows to add Suggested Items", "customizableInfo": "it allows to Customize the Product", "treatYourselfInfo": "Suggest me a good info message", "isAllergyPopUp": "Allergy <PERSON>up", "maxSelectection": "Max Items Selection"}, "menu": {"page-title": "Menus", "title": "Title", "image": "Image", "active": "Active", "action": "Action", "add": "<PERSON><PERSON>", "type": "Type", "today": "Today", "treat_your_self": "Treat Your Self", "ice_cream": "Ice Cream", "description": "Description", "upload-image": "Upload Image", "selectMenuCategories": "Select Parent Category", "menuCategories": "Category", "details": "Details", "edit": "Edit", "take_home_treat": "Take Home Treat", "fondue": "Fondue", "acai_bowls": "Acai Bowls", "drink_and_extra": "Drink & Extras", "comfort": "Comfort", "buddha_bowls": "Buddha Bowls", "stir_fry": "Stir Fry", "pies": "Pies", "cake": "Cake", "pints_quarts": "Pints Quarts", "flying_sauce": "Flying Sauce", "push_pops": "Push Pops", "parentView": "Parent View", "featuredView": "Featured View", "selfView": "Self View", "layoutOne": "Layout One", "layoutTwo": "Layout Two", "productLayoutView": "Product Layout View", "menuViewType": "Menu View Type", "update": "Update Menu", "layerImage": "Select Layer Image"}, "common": {"active": "Active", "inactive": "In Active", "edit": "Edit", "activate": "Activate", "deactivate": "Deactivate", "click-to-upload-file": "Click to upload file", "loading": "Loading...", "notFound": "Not Found", "status": "Status", "notifications": "Notifications", "orders": "Orders", "transactions": "Transactions", "paymentMethod": "Payment Method", "paymentStatus": "Payment Status", "orderId": "Order ID", "refId": "Transaction Reference Id", "orderName": "Order Name", "caterings": "Caterings", "modifiers": "Modifiers", "userName": "User Name", "orderPlatform": "Platform", "totalAmount": "Amount"}, "staff": {"page-title": "Staffs", "name": "Name", "email": "Email", "password": "Password", "phone": "Phone", "image": "Image", "add": "Add Staff", "upload-image": "Upload Image", "details": "Details", "edit": "Edit", "title": "Title"}, "branch": {"add": "Add Branch", "update": "Update Branch", "confirmPassword": "Confirm Password", "address": "Address", "city": "City"}, "catering": {"firstName": "First Name", "fullName": "Full Name", "lastName": "Last Name", "email": "Email", "phone": "Phone No."}, "order": {"fullName": "Full Name", "orderId": "Order ID", "orderStatus": "Order Status"}, "modifier": {"add": "Add Modifier", "edit": "Edit", "title": "Title"}, "menuLayout": {"menuPosition": "Select Menu Position", "menuDeviceType": "Select Menu Device Type", "menuType": "Select Menu Type"}, "promocode": {"add": "Add Promocode", "discountType": "Select discount Type", "menuType": "Select Menu Type"}}