import { FC } from "react";
import { But<PERSON> } from "react-bootstrap";
import { FaPencilAlt } from "react-icons/fa";
import { Link } from "react-router-dom";
import useTranslate from "hooks/useTranslate";

const EditActionComponent: FC<{ url: string }> = ({ url }) => {
  const { translate } = useTranslate();
  return (
    <Link to={url}>
      <Button size="sm" variant="primary">
        <FaPencilAlt title={translate("common.edit")} />
      </Button>
    </Link>
  );
};

export default EditActionComponent;
