import { LoginInitial } from "constants/InitialValues";
import { LoginSchema } from "constants/Validation";
import { FormikProvider, Form as FormikForm, useFormik } from 'formik';
import { Card, Container, Row, Image, FloatingLabel, Form, Button } from "react-bootstrap"
import logo from "assets/images/logo.png";
import { useBoolean } from "hooks/useBoolean";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { authService } from "services";
import { toast } from "react-toastify";
import useCurrentUser from "hooks/useCurrentUser";


const Login = () => {

  const password = useBoolean(false);
  const { setToken } = useCurrentUser();
  const navigate = useNavigate();

  const formik = useFormik({
    initialValues: LoginInitial,
    validationSchema: LoginSchema,
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(false);
      await authService.login({ ...values, "role": "RESTAURANT" }).then((response) => {
        toast.success(response?.data?.message);
        setToken(response?.data?.data?.token);
        localStorage.setItem("token", response?.data?.data?.token);
        if (response?.data?.success) {
          navigate("/dashboard");
        }
      }).catch((error) => {
        toast.error(error?.data?.message);

      });
    }
  });


  const { errors, touched, isSubmitting, getFieldProps, handleSubmit } = formik;
  return (
    <Container>
      <Row>
        <div
          className="card-box d-flex justify-content-center align-items-center "
          style={{ minHeight: "100vh" }}
        >
          <Card className="bg-transparent">
            <Card.Body>
              <div className="text-center">
                <Image src={logo} alt="logo" className="card-box-logo" />
              </div>
              <Card.Title className="text-center mb-3">Login</Card.Title>
              <Card.Subtitle className="text-center mb-3">
                Enter your email address and password to access restaurant panel
              </Card.Subtitle>
              <FormikProvider value={formik}>
                <FormikForm autoComplete="off" noValidate>
                  <FloatingLabel className="mb-3" label="Email" controlId="email">
                    <Form.Control
                      type="email"
                      {...getFieldProps('email')}
                    />
                    {touched.email && errors.email && (
                      <small className="text-danger">{errors.email}</small>
                    )}
                  </FloatingLabel>
                  <FloatingLabel
                    className="mb-3"
                    label="Password"
                    controlId="password"
                  >
                    <Form.Control type={password.value ? "text" : "password"}
                      {...getFieldProps('password')}
                    />
                    <Button
                      className="eye-icon"
                      onClick={password.onToggle}                  >
                      {password.value ? <FaEyeSlash /> : <FaEye />}
                    </Button>
                    {touched.password && errors.password && (
                      <small className="text-danger">{errors.password}</small>
                    )}
                  </FloatingLabel>

                  <Button variant="primary" className="mb-3 card-box-btn" disabled={isSubmitting} onClick={(event) => {
                    event.preventDefault();
                    handleSubmit()
                  }}>
                    {isSubmitting ? "Logging..." : "Login"}
                  </Button>
                  <div className="text-center">
                    <Link to="/forgot-password">Forgot Password?</Link>
                  </div>
                </FormikForm>

              </FormikProvider>
            </Card.Body>
          </Card>
        </div>
      </Row>
    </Container>
  )
}

export default Login