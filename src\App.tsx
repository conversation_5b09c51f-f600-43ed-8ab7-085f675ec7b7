import { ToastContainer } from "react-toastify";
import { Routing } from "routing";
import "bootstrap/dist/css/bootstrap.min.css";
import "react-toastify/dist/ReactToastify.css";
import "styles/common.scss";
import { isMobile } from "react-device-detect";

function App() {
  return (
    <div className={`body-wrapper ${isMobile ? "mobile" : ""}`}>
      <Routing />
      <ToastContainer position="bottom-center" theme="light" />
    </div>
  );
}

export default App;
