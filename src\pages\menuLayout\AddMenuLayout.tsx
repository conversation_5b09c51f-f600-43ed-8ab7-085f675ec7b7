import React, { useEffect, useState } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import Select from "react-select";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { menuService } from "services";
import { useNavigate } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { MENU_POSITIONS, MENU_TYPE } from "constants/InitialValues";

const options = [
  { value: 'react', label: 'React' },
  { value: 'vue', label: 'Vue.js' },
  { value: 'angular', label: 'Angular' },
  { value: 'svelte', label: 'Svelte' },
  { value: 'nextjs', label: 'Next.js' },
  { value: 'nuxtjs', label: 'Nuxt.js' },
  { value: 'nodejs', label: 'Node.js' },
  { value: 'express', label: 'Express.js' }
];

// Helper function to create validation schema
const createValidationSchema = () =>
  yup.object().shape({
    menuId: yup.number().required("Menu is required"),
    menuDeviceType: yup.string().required("Menu device Type is required"),
    menuPosition: yup.string().when("menuDeviceType", (menuDeviceType, schema) => {
      return menuDeviceType[0] === "KIOSK"
        ? schema.required("Menu Position is required")
        : schema.notRequired();
    }),
    order: yup.number().required("Order is required").min(1, "Order must be at least 1"), // Added validation
    menuType: yup.string().required("Menu Type is required")
  });

// Main component
const AddMenuLayout: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState(options)

  const initialValues = {
    menuId: 0,
    menuPosition: "TOP",
    menuDeviceType: "",
    menuType: "",
    order: "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: createValidationSchema(),
    onSubmit: async (values) => {
      try {
        await menuService.addLayout(values);
        toast.success("Category added successfully");
        navigate('/menu-layout')
      } catch (err) {
        console.error("Error adding category:", err);
        toast.error("Failed to add category");
      }
    },
  });

  const handleOnChange = (res: any) => {
    formik.setFieldValue("menuId", Number(res?.id))
    formik.setFieldValue("menuType", res?.type)
  }

  const handleInputChange = (inputValue: string) => {
    if (inputValue?.length === 0) {
      setSearchTerm("a")
    }
    else {
      setSearchTerm(inputValue); // Update state when user types
    }
  };

  useEffect(() => {
    (async () => {
      const data: any = await menuService.search(searchTerm)
      if (data?.data?.success) {
        setData(data?.data?.data?.searchResult)
      }
    })()
  }, [])

  useEffect(() => {
    const handler = setTimeout(() => {
      if (searchTerm) {
        (async () => {
          const data: any = await menuService.search(searchTerm)
          if (data?.data?.success) {
            setData(data?.data?.data?.searchResult)
          }
        })()
      }
    }, 300);

    return () => clearTimeout(handler); // Cleanup on unmount or when input changes
  }, [searchTerm]);


  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isAdd: true, entityPath: "/menu-layout", entities: "Menu Layout" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{translate("menu.add")}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <Select
                  isSearchable
                  options={data}
                  getOptionLabel={(e: any) => `${e.title} (${e.type})`}
                  onChange={handleOnChange}
                  onInputChange={handleInputChange}
                  placeholder="Search Menu type ..."
                  styles={{
                    control: (base) => ({
                      ...base,
                      borderColor: "#ced4da", // Bootstrap default border color
                      boxShadow: "none", // Remove focus border
                      "&:hover": { borderColor: "#80bdff" }, // Optional: Bootstrap hover effect
                    }),
                    option: (base, { isFocused, isSelected }) => ({
                      ...base,
                      backgroundColor: (() => {
                        if (isSelected) return "#f8f9fa";
                        if (isFocused) return "#e9ecef";
                        return "white";
                      })(),
                      color: "black",
                    }),
                  }}
                />

                <Form.Control.Feedback type="invalid">
                  {formik.touched.menuId && formik.errors.menuId}
                </Form.Control.Feedback>
              </Col>

              <Col md="6" className="mt-3">
                <FloatingLabel controlId="order" label="Order" className="mb-3">
                  <Form.Control
                    type="number"
                    name="order"
                    value={formik.values.order}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.order && !!formik.errors.order}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.order && formik.errors.order}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel controlId="menuDeviceType" label={translate("menuLayout.menuDeviceType")} className="mb-3" style={{ zIndex: 0 }}>
                  <Form.Select
                    name="menuDeviceType"
                    value={formik.values.menuDeviceType}
                    // onChange={formik.handleChange}
                    onChange={(res: any) => {
                      formik.setFieldValue("menuDeviceType", res?.target?.value)
                      formik.setFieldValue("menuPosition", res?.target?.value === "WEB" ? "TOP" : "BOTTOM")
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.menuDeviceType && !!formik.errors.menuDeviceType}
                  >
                    <option value={0} key={0}> Select </option>
                    {
                      MENU_TYPE.map((value: string) => (
                        <option value={value} key={value}>
                          {value}
                        </option>
                      ))
                    }
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.menuDeviceType && formik.errors.menuDeviceType}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              {
                formik.values.menuDeviceType === "KIOSK" &&
                <Col md="6">
                  <FloatingLabel controlId="menuPosition" label={translate("menuLayout.menuPosition")} className="mb-3">
                    <Form.Select
                      name="menuPosition"
                      value={formik.values.menuPosition}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      isInvalid={formik.touched.menuPosition && !!formik.errors.menuPosition}
                    >
                      <option value={0} key={0}> Select </option>
                      {MENU_POSITIONS.map((value: string) => (
                        <option value={value} key={value}>
                          {value}
                        </option>
                      ))}
                    </Form.Select>
                    <Form.Control.Feedback type="invalid">
                      {formik.touched.menuPosition && formik.errors.menuPosition}
                    </Form.Control.Feedback>
                  </FloatingLabel>
                </Col>
              }
            </Row>

            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default AddMenuLayout;
