import base from "./base.service";
import { StaffInterface } from "interfaces";

const getAll = (payload: any) => base.get("/staff", { params: payload });
const updateStatus = (id: number | string) =>
  base.get(`/staff/${id}/update-status`);
const remove = (id: number | string) => base.delete(`/staff/${id}`);
const add = (payload: StaffInterface) => base.post(`/staff`, { ...payload });
const getDetail = (id: string | number) => base.get(`staff/${id}`);
const update = (id: string | number, payload: any) =>
  base.put(`staff/${id}`, { ...payload });
const permissionList = () => base.get("/staff/permission-list");

export const staffService = {
  getAll,
  updateStatus,
  remove,
  add,
  getDetail,
  update,
  permissionList,
};
