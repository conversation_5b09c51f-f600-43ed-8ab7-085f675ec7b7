import { confirmAlert } from "react-confirm-alert";
import "react-confirm-alert/src/react-confirm-alert.css"; // Import the CSS for styling

const useConfirmAlert = () => {
  const showConfirmAlert = (message: string, onConfirm: () => void) => {
    confirmAlert({
      title: "Are you sure",
      message,
      buttons: [
        {
          label: "Cancel",
          onClick: () => {},
          className:"btn btn-secondary"
        },
        {
          label: "Confirm",
          onClick: onConfirm,
          className:"btn btn-primary"
        },
      ],
    });
  };

  return showConfirmAlert;
};

export default useConfirmAlert;
