import base from "./base.service";

const getAll = (payload: any) => base.get("/restaurant-branch", { params: payload });
const updateStatus = (id: number | string) =>
    base.get(`/restaurant-branch/${id}/update-status`);
const remove = (id: number | string) => base.delete(`/restaurant-branch/${id}`);
const add = (payload: any) => base.post(`/restaurant-branch`, { ...payload });
const getDetail = (id: string | number) => base.get(`restaurant-branch/${id}`);
const update = (id: string | number, payload: any) =>
    base.put(`restaurant-branch/${id}`, { ...payload });
const permissionList = () => base.get("/staff/permission-list");
const upload = (payload: any) => base.post("/restaurant-branch/upload", payload);

const getSalesReport = () => base.get("/sales-and-report");


export const branchService = {
    getAll,
    updateStatus,
    remove,
    add,
    getDetail,
    update,
    permissionList,
    upload,
    getSalesReport
};
