import base from "./base.service";

const list = (payload: any) => base.get(`/order?page=${payload?.page}&pagination=false&isParent=false&limit=10&activeStatus=ALL&sortBy=id&sortDirection=DESC`);


const updateStatus = (id: string | number) => base.get(`order/${id}/update-status`);

const getDetail = (id: | string | number) => base.get(`order/${id}`);

export const orderService = {
    list,
    updateStatus,
    getDetail,
};
