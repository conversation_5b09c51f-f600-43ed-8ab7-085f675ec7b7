import { ItemInterface } from "interfaces";
import base from "./base.service";

const list = (payload: any) => base.get(`/item`, { params: payload });

const add = (payload: ItemInterface) => base.post(`/item`, { ...payload });

const updateStatus = (id: string | number) => base.get(`item/${id}/update-status`)

const getDetail = (id: | string | number) => base.get(`item/${id}`)

const updateItem = (id: string | number, payload: any) => base.put(`item/${id}`, { ...payload })

const deleteItem = (id: string | number) => base.delete(`item/${id}`)

export const itemService = {
  list,
  add,
  updateStatus,
  getDetail,
  updateItem,
  deleteItem
};
