import base from "./base.service";
import { MenuCategoryInterface } from "interfaces";


const getMenuCategories = (payload: any) => base.get("/menu-categories", { params: payload });
const updateCategoryStatus = (id: number | string) => base.get(`/menu-categories/${id}/update-status`);
const getParentMenuCategories = (payload: any) => base.get("/menu-categories", { params: payload });
const getSubMenuCategories = (id: number | string) => base.get(`/menu-categories/sub-category/${id}`);
const add = (payload: MenuCategoryInterface) => base.post(`/menu-categories/create`, { ...payload });
const getDetail = (id: | string | number) => base.get(`menu-categories/${id}`)
const update = (id: string | number, payload: any) => base.put(`menu-categories/${id}`, { ...payload });
const deleteCategory = (id: number | string) => base.delete(`/menu-categories/${id}`);
const addLayout = (payload: any) => base.post(`/menu-categories/layout`, { ...payload });
const search = (keyword: string) => base.get(`/menu-categories/search?search=${keyword}`);
const getMenuLayoutList = (payload: any) => base.get("/menu-categories/layout", { params: payload });
const deleteMenuLayout = (id: number | string) => base.delete(`/menu-categories/layout/${id}`);
const getMenuLayoutDetails = (id: number | string) => base.get(`/menu-categories/layout/${id}`);
const updateMenuLayoutDetails = (id: number | string, payload: any) => base.put(`/menu-categories/layout/${id}`, { ...payload });
// const updateProfile = (payload: UserProfile) => base.put("/my-account/profile", payload);
// const changePassword = (payload: any) => base.put("/my-account/change-password", payload);
// const logout = () => base.get("/my-account/logout");

export const menuService = {
    getMenuCategories,
    updateCategoryStatus,
    getParentMenuCategories,
    getSubMenuCategories,
    add,
    getDetail,
    update,
    deleteCategory,
    addLayout,
    search,
    getMenuLayoutList,
    deleteMenuLayout,
    getMenuLayoutDetails,
    updateMenuLayoutDetails
};
