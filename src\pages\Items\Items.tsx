import AddActionComponent from "components/AddActionComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import ImageModal from "components/ImageModal";
import SearchInputComponent from "components/SearchInputComponent";
import StatusActionComponent from "components/StatusActionComponent";
import StatusComponent from "components/StatusComponent";
import ViewActionComponent from "components/ViewActionComponent";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
// import { itemsListBreadcrumbs } from "constants/breadcrums";
import useTranslate from "hooks/useTranslate";
import { CustomizationInterface, ItemInterface } from "interfaces";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { itemService } from "services";
import { toast } from "react-toastify";
import DeleteActionComponent from "components/DeleteActionComponent";
import DeleteModal from "components/Modal/DeleteModal";

const Items = () => {
  const { translate } = useTranslate();
  const [data, setData] = useState<CustomizationInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");
  const [show, setShow] = useState<any>(false);
  const [id, setId] = useState<any>("");
  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);

  const updateStatus = async (id: number) => {
    try {
      await itemService.updateStatus(id);
      setData((prevData) =>
        prevData.map((item) =>
          item.id === id ? { ...item, isSuspended: !item.isSuspended } : item
        )
      );
    } catch (error) { }
  };

  const removeRecord = async (id: number | string) => {
    setId(id)
    handleShow()

  };

  const handleDelete = async (id: any) => {
    try {
      await itemService.deleteItem(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData => prevData.filter(staff => staff.id !== id));
    } catch (error) {
      console.error(error);
    }
    handleClose(); // Close the modal after delete
  };

  const renderActionColumn = (row: ItemInterface) => {
    return (
      <>
        <StatusActionComponent
          id={Number(row.id)}
          isSuspended={Boolean(row.isSuspended)}
          updateStatus={updateStatus}
        />{" "}
        <EditActionComponent url={`/items/${row.id}/edit`}></EditActionComponent> {" "}
        <ViewActionComponent url={`/items/${row.id}/view`} />{" "}
        <DeleteActionComponent fn={() => removeRecord(Number(row.id))} />{" "}
      </>
    );
  };
  const fetchData = async () => {
    setLoading(true);
    const payload = {
      page,
      pagination: true,
      skip: (page - 1) * perPage,
      limit: perPage,
      sortBy,
      sortDirection,
      searchTerm,
      activeStatus: "ALL",
    };
    try {
      const response = await itemService.list(payload);
      setData(response.data.data.docs);
      setTotalRows(response.data.data.meta.totalDocs);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryStatus = (isSuspended: boolean) => {
    return <StatusComponent isSuspended={!isSuspended} />;
  };

  const columns = [
    {
      name: "Title",
      sortable: true,
      sortField: "title",
      selector: (row: CustomizationInterface) => row.title || "N/A",
    },
    {
      name: "Image",
      sortable: false,
      sortField: "image",
      selector: (row: CustomizationInterface) => (
        <ImageModal
          height={"20px"}
          thumbnailSrc={row.image}
          altText={row.title}
        />
      ),
    },
    {
      name: "Status",
      sortable: false,
      sortField: "isSuspended",
      selector: (row: CustomizationInterface) => renderCategoryStatus(!row.isSuspended),
    },
    {
      name: "Modifier",
      sortable: false,
      sortField: "isSuspended",
      selector: (row: any) => row.customizationTitle,
    },
    {
      name: "Action",
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [page, perPage, sortBy, sortDirection, searchTerm]);

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/items", entities: "Items" })} />

      {/* <BreadcrumComponent breadcrumbs={itemsListBreadcrumbs} /> */}
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center custom-card-header-title">
              <FaList />
              <h4 className="mb-0">{translate("items.items")}</h4>
            </div>
            <div>
              <AddActionComponent url="/items/add" />
            </div>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex gap-3 pb-4 search-box">
            <SearchInputComponent
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          <DatatableComponent
            columns={columns}
            data={data}
            loading={loading}
            totalRows={totalRows}
            setPage={setPage}
            setPerPage={setPerPage}
            setSortBy={setSortBy}
            setSortDirection={setSortDirection}
          />

          <DeleteModal title="Item" id={id} show={show} handleDelete={handleDelete} handleClose={handleClose} />
        </Card.Body>
      </Card>
    </>
  );
};

export default Items;
