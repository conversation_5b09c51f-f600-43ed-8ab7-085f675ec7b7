import React, { useEffect, useState } from "react";
import {
  Card,
  Accordion,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import Select, { MultiValue } from "react-select";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import ImageUploadComponent from "components/ImageUploadComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { CustomizationInterface, ItemInterface, MenuCategoryInterface, ProductAddInterface } from "interfaces";
import { menuService, customizationService, itemService, productService } from "services";
// import { productAddBreadcrumbs } from "constants/breadcrums";
import CustomizationSelect from "components/ProductCustomizer/CustomizationSelect";
import ItemList from "components/ProductCustomizer/ItemList";
import { useNavigate } from "react-router-dom";
import './Product.css';
import { Tooltip } from "react-tooltip";
import { FaInfoCircle } from "react-icons/fa";
import "react-tooltip/dist/react-tooltip.css";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import CategoryAccordion from "./CategoryAccordion";


// Helper function to create validation schema

const createValidationSchema = (categoryData: any) =>
  yup.object().shape({
    title: yup.string()
      .trim()
      .required("Name is required")
      .min(3, "Name must be at least 3 characters")
      .max(50, "Name cannot exceed 50 characters"),
    image: yup.string().required("Image is required"),
    backgroundImage: yup.string().required("Background Image is required"),
    type: yup.string().required("Type is required"),
    productType: yup.string().required("Product Type is required"),
    description: yup.string()
      .trim()
      .min(3, "Description must be at least 3 characters")
      .max(200, "Description cannot exceed 200 characters"),
    price: yup
      .number()
      .required("Price is required")
      .positive("Price must be a positive number")
      .min(0, "Price must be greater than or equal to 0")
      .max(999999, "Price must be lower than or equal to 999999"),
    isSuggested: yup.boolean(),
    isAllergyPopUp: yup.boolean(),
    suggestionProduct: yup
      .array()
      .when("isSuggested", {
        is: (value: any) => value, // Checks if isSuggested is true
        then: () => yup
          .array()
          .of(yup.number().required("Suggestion ID is required"))
          .required("Suggested products are required")
          .min(1, "At least one suggestion is required"), // Ensures there is at least one item in the array
        otherwise: () => yup.array().nullable(),
      }),
    menuCategoryId: yup
      .number()
      .required("Menu Category is required")
      .positive("Menu Category must be selected"),
    // subMenuCategoryId: yup.number(),
    // subMenuCategoryId: yup
    //   .number()
    //   .nullable()
    //   .test(
    //     "required-if-submenu-exists",
    //     "Sub Menu Category is required when sub menu categories are available",
    //     (value) => {
    //       if (categoryData?.length > 0) {
    //         return value !== null && value !== undefined;
    //       }
    //       return true; // Not required if subMenuCategories length is 0
    //     }
    //   ),
    productCustomizations: yup
      .array()
      .of(
        yup.object().shape({
          id: yup.number().required("Customization ID is required"),
          items: yup
            .array()
            .of(
              yup.object().shape({
                itemId: yup.number().required("Item ID is required"),
                isAdded: yup.boolean().required("Item addition status is required"),
              })
            )
            .required("Items are required")
            .min(0),
          maxItemsAllowed: yup.number(),
          maxItems: yup.number().min(0, "Max items must be zero or greater"),
          isRequired: yup.boolean().required(),
        })
      )
      .min(0, "At least one customization is required"),
  });


const useProductData = () => {

  const [data, setData] = useState<{
    menuCategories: MenuCategoryInterface[];
    customizationOptions: any[];
    itemsList: any[];
    suggestionProducts: any[];
  }>({
    menuCategories: [],
    customizationOptions: [],
    itemsList: [],
    suggestionProducts: [],
  });



  useEffect(() => {
    const fetchData = async () => {
      try {
        const [menus, customizations, items, suggestionProducts] = await Promise.all([
          menuService.getParentMenuCategories({ page: 1, pagination: false, activeStatus: "ALL", isParent: false }),
          customizationService.list({ page: 1, pagination: false, activeStatus: "ALL" }),
          itemService.list({ page: 1, pagination: false, activeStatus: "ALL" }),
          productService.suggestionProductList({ page: 1, pagination: false, activeStatus: "ALL" }),
        ]);

        const groupedData = items?.data?.data?.item.reduce((acc: any, item: any) => {
          const { customizationId, id, title, itemType } = item;

          // Find existing group or create a new one
          let group = acc.find((g: any) => g.customizationId === customizationId);
          if (!group) {
            group = { customizationId, list: [] };
            acc.push(group);
          }

          // Push transformed object to the list
          group.list.push({ id, value: id, label: title, itemType: itemType });

          return acc;
        }, []);

        setData({
          menuCategories: menus?.data?.data?.menuCategory || [],
          customizationOptions: customizations?.data?.data?.customization?.map((c: CustomizationInterface) => ({
            id: c.id,
            value: c.type,
            label: c.title,
          })) || [],
          itemsList: groupedData || [],
          suggestionProducts: suggestionProducts.data.data.product || [],
        });
      } catch (error) {
        console.error(error);
        toast.error("Failed to load data");
      }
    };

    fetchData();
  }, []);

  return { data, setData };
};



// Main component
const ProductAdd: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate()
  const { data: { menuCategories, customizationOptions, itemsList, suggestionProducts } } = useProductData();
  const [selectedCategoryType, setSelectedCategoryType] = useState<string>("")

  const [selectedCustomization, setSelectedCustomization] = useState<
    { id: string; label: string; value: string }[]
  >([]);

  const [items, setItems] = useState<{
    [key: string]: {
      value: string;
      label: string;
      selected?: boolean;
      quantity?: number;
    }[];
  }>({});

  const [subMenuCategories, setSubMenuCategories] = useState<MenuCategoryInterface[]>([]);

  const handleQuantityAddedChange = (
    customizationId: string,
    itemId: number,
    isAddedValue: boolean
  ) => {
    // Get the index of the customization in Formik's values
    const customizationIndex = formik.values.productCustomizations.findIndex(
      (c) => c.id === customizationId
    );

    if (customizationIndex !== -1) {
      // Get the current customization
      const customization = formik.values.productCustomizations[customizationIndex];

      // Update the items for this customization
      const updatedItems = customization.items.map((item: { itemId: number; }) =>
        item.itemId === itemId
          ? { ...item, isAdded: isAddedValue }
          : item
      );

      // Update Formik's state
      formik.setFieldValue(
        `productCustomizations[${customizationIndex}]`,
        { ...customization, items: updatedItems }
      );
    }
  };



  // Fetch sub menu categories based on the selected menu category
  const fetchSubMenuCategories = async (parentId: number) => {
    try {
      const { data } = await menuService.getSubMenuCategories(parentId);
      setSubMenuCategories(data.data.menuCategory || []);
    } catch (error) {
      toast.error("Failed to load sub menu categories");
    }
  };

  const initialValues = {
    title: "",
    image: "",
    backgroundImage: "",
    type: "PRESETS",
    productType: "SWEET",
    price: 0,
    isCustomizable: false,
    isSuggested: false,
    isAllergyPopUp: false,
    isTreatYourSelf: false,
    needAssistant: false,
    description: "",
    menuCategoryId: 0,
    subMenuCategoryId: 0,
    suggestionProduct: [] as any[],
    productCustomizations: [] as any[],
  };

  const formik = useFormik({
    initialValues,
    validationSchema: createValidationSchema(subMenuCategories),
    onSubmit: async (values) => {
      try {
        // Filter out customizations with empty items array
        const filteredCustomizations = values.productCustomizations.filter(
          (customization) => customization.items && customization.items.length > 0
        );

        // Create a new values object with filtered customizations
        const updatedValues = {
          ...values,
          ...(filteredCustomizations.length > 0 && { productCustomizations: filteredCustomizations }),
          ...(values.suggestionProduct.length > 0 && { suggestionProduct: values.suggestionProduct }),
        };
        // Implement submit logic here
        await productService.add(updatedValues as unknown as ProductAddInterface);
        toast.success("Product added successfully");
        navigate('/products')
      } catch (err) {
        console.error("Error adding product:", err);
        toast.error("Failed to add product");
      }
    },
  });

  const handleCustomizationSelection = (
    selected: MultiValue<{ id: string; value: string; label: string }>
  ) => {

    // Convert selected options to a map for quick lookup
    const selectedCustomizationMap = new Map(
      selected.map((item: any) => [item.id, { id: item.id, label: item.label, value: item.value, itemType: item?.itemType }])
    );

    // Get current customizations from Formik
    const currentCustomizations = formik.values.productCustomizations;

    // Create a map for current customizations for quick lookup
    const currentCustomizationMap = new Map(
      currentCustomizations.map((c) => [c.id, c])
    );

    // Add new selections or update existing ones
    const updatedCustomizations = currentCustomizations.map((customization) => {
      if (selectedCustomizationMap.has(customization.id)) {
        // Update existing customization with the latest data
        return {
          ...customization,
          ...selectedCustomizationMap.get(customization.id),
        };
      }
      return customization;
    });

    // Add new customizations that were not previously in the form state
    selected.forEach((option) => {
      if (!currentCustomizationMap.has(option.id)) {
        updatedCustomizations.push({
          id: option.id,
          items: [],
          maxItems: 0,
          isRequired: false,
        });
      }
    });

    // Update Formik's field value
    formik.setFieldValue("productCustomizations", updatedCustomizations);

    // Update local state for selected customizations
    setSelectedCustomization(
      selected.map((item: any) => ({
        id: item.id,
        label: item.label,
        value: item.value,
        itemType: item?.itemType
      }))
    );

  };


  const handleCustomization = (
    selectedItems: MultiValue<{ value: string; label: string, itemType: string }>,
    customization: { id: string; value: string; label: string },
    customizationIndex: number
  ) => {

    const itemsForCustomization = selectedItems.map((item) => ({
      value: item.value,
      label: item.label,
      itemType: item.itemType,
    }));

    // Update local state for items
    setItems((prevItems) => ({
      ...prevItems,
      [customization.value]: itemsForCustomization,
    }));

    // Retrieve existing customization from Formik's state
    const existingCustomization = formik.values.productCustomizations.find(
      (c) => c.id === customization.id
    );

    // Create payload with updated values
    const payload = {
      id: customization.id,
      items: selectedItems.map((item) => ({
        itemId: item.value,
        isAdded: false
      })),
      maxItems: existingCustomization ? existingCustomization.maxItems : 0,
      isRequired: existingCustomization ? existingCustomization.isRequired : false,
    };

    // Update Formik's state
    updateCustomizationsItems(payload, customizationIndex);
  };


  const updateCustomizationsItems = (
    customizationValue: any,
    index: number
  ) => {
    const updatedCustomizations = [...formik.values.productCustomizations];
    updatedCustomizations[index] = customizationValue;
    formik.setFieldValue("productCustomizations", updatedCustomizations);
  };

  const handleCheckboxChange = (
    customizationId: string,
    isRequired: boolean,
    index: number
  ) => {
    // Find the customization index in Formik's values
    const customizationIndex = formik.values.productCustomizations.findIndex(
      (c) => c.id === customizationId
    );

    if (customizationIndex !== -1) {
      // Get the current customization from Formik's values
      const customization = formik.values.productCustomizations[customizationIndex];

      // Update the customization with the new isRequired value
      const updatedCustomization = {
        ...customization,
        isRequired,
      };

      // Update Formik's state with the new customization
      const updatedCustomizations = [...formik.values.productCustomizations];
      updatedCustomizations[customizationIndex] = updatedCustomization;

      formik.setFieldValue("productCustomizations", updatedCustomizations);
    }
  };

  // Handle menu category change and fetch corresponding subcategories
  const handleMenuCategoryChange = (selectedOption: any) => {
    setSelectedCategoryType(menuCategories?.find((response: any) => response?.id === Number(selectedOption?.value))?.type || "");
    const menuCategoryId = selectedOption ? selectedOption.value : null;
    formik.setFieldValue("menuCategoryId", Number(menuCategoryId));
    formik.setFieldValue("subMenuCategoryId", ""); // Reset subMenuCategoryId on parent change
    if (menuCategoryId) {
      fetchSubMenuCategories(menuCategoryId);
    }
  };

  console.log('selectedCustomization', customizationOptions)

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isAdd: true, entityPath: "/products", entities: "Products" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{translate("product.add")}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <FloatingLabel controlId="title" label={translate("product.title")} className="mb-3">
                  <Form.Control
                    type="text"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.title && !!formik.errors.title}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.title && formik.errors.title}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="type" label="Select Type" className="mb-3">
                  <Form.Select
                    name="type"
                    value={formik.values.type}
                    onChange={(value: any) => {
                      if (value?.target?.value !== "PRESETS") {
                        formik.setFieldValue("isTreatYourSelf", false)
                      }
                      formik.handleChange(value)
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.type && !!formik.errors.type}
                  >
                    <option value="PRESETS">{translate("product.presets")}</option>
                    <option value="CREATEOWN">{translate("product.createown")}</option>
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.type && formik.errors.type}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel
                  controlId="description"
                  label={translate("product.description")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={
                      formik.touched.description && !!formik.errors.description
                    }
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.description && formik.errors.description}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="price" label={translate("product.price")} className="mb-3">
                  <Form.Control
                    type="text" // Using text to allow decimal input
                    inputMode="decimal"
                    pattern="[0-9]*[.,]?[0-9]*"
                    name="price"
                    value={formik.values.price}
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only numbers and a single decimal point
                      value = value.replace(/[^0-9.]/g, '');

                      // Prevent multiple decimal points
                      if (value.split('.').length > 2) {
                        value = value.slice(0, value.lastIndexOf('.'));
                      }

                      formik.setFieldValue("price", value);
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.price && !!formik.errors.price}
                  />

                  {/* <Form.Control
                    type="number"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    name="price"
                    value={formik.values.price}
                    onChange={(e) => {
                      // Allow only digits by filtering out non-numeric characters
                      const value = e.target.value.replace(/\D/g, '');
                      formik.setFieldValue("price", value);
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.price && !!formik.errors.price}
                  /> */}
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.price && formik.errors.price}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
            </Row>

            <Row>


              <Col md="6" className="d-flex align-items-center mt-2">
                <Col md="6" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isSuggested"
                    name="isSuggested"
                    label="Suggested"
                    checked={formik.values.isSuggested}
                    onChange={(value) => {
                      formik.setFieldValue("suggestionProduct", [])
                      formik.handleChange(value)
                    }}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isSuggested-tooltip"
                      data-tooltip-content={`${translate("product.suggestedInfo")}`}
                    />
                    <Tooltip
                      id="isSuggested-tooltip"
                      place="top"
                    />
                  </div>
                </Col>
                {
                  // formik.values.type !== "PRESETS" &&
                  <Col md="6" className="d-flex align-items-center">
                    <Form.Check
                      type="checkbox"
                      id="isCustomizable"
                      name="isCustomizable"
                      label="Customizable"
                      checked={formik.values.isCustomizable}
                      onChange={formik.handleChange}
                    />
                    <div className="d-flex align-items-center ms-2">
                      <FaInfoCircle
                        className="info-icon"
                        data-tooltip-id="isCustomizable-tooltip"
                        data-tooltip-content={`${translate("product.customizableInfo")}`}
                      />
                      <Tooltip
                        id="isCustomizable-tooltip"
                        place="top"
                      />
                    </div>
                  </Col>
                }

                <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isAllergyPopUp"
                    name="isAllergyPopUp"
                    label="Allergy Popup"
                    checked={formik.values.isAllergyPopUp}
                    onChange={(value) => {
                      formik.handleChange(value)
                    }}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isAllergyPopUp-tooltip"
                      data-tooltip-content={`${translate("product.isAllergyPopUp")}`}
                    />
                    <Tooltip
                      id="isAllergyPopUp-tooltip"
                      place="top"
                    />
                  </div>
                </Col>

                <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isTreatYourSelf"
                    name="isTreatYourSelf"
                    label="Treat YourSelf"
                    checked={formik.values.isTreatYourSelf}
                    onChange={formik.handleChange}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isTreatYourSelf-tooltip"
                      data-tooltip-content={`${translate("product.treatYourselfInfo")}`}
                    />
                    <Tooltip
                      id="isTreatYourSelf-tooltip"
                      place="top"
                    />
                  </div>
                </Col>
                <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="needAssistant"
                    name="needAssistant"
                    label={translate("product.needAssist")}
                    checked={formik.values.needAssistant}
                    onChange={formik.handleChange}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="needAssistant-tooltip"
                      data-tooltip-content={`${translate("product.needAssistInfo")}`}
                    />
                    <Tooltip
                      id="needAssistant-tooltip"
                      place="top"
                    />
                  </div>
                </Col>
              </Col>

              <Col md="12" className="mt-4">
                <FloatingLabel controlId="productType" label="Product Type" className="mb-3">
                  <Form.Select
                    name="productType"
                    value={formik.values.productType}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.productType && !!formik.errors.productType}
                  >
                    <option value="SWEET">{translate("product.sweet")}</option>
                    <option value="SPICY">{translate("product.spicy")}</option>
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.productType && formik.errors.productType}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

            </Row>

            <Row>
              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("product.upload-image")} (335px x 265px)</Form.Label>
                  <ImageUploadComponent
                    bucketType="products"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.image}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("image", "")}
                    fieldName="image"
                  />
                  {formik.touched.image && formik.errors.image && (
                    <small className="text-danger">{formik.errors.image}</small>
                  )}
                </Form.Group>
              </Col>
              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("product.background-image")}</Form.Label>
                  <ImageUploadComponent
                    bucketType="products"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.backgroundImage}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("backgroundImage", "")}
                    fieldName="backgroundImage"
                  />
                  {formik.touched.backgroundImage && formik.errors.backgroundImage && (
                    <small className="text-danger">{formik.errors.backgroundImage}</small>
                  )}
                </Form.Group>
              </Col>
            </Row>

            {/* <Row>
              <Col md="6">
                <h4>{translate("product.selectMenuCategories")}</h4>
                <Select
                  className="mb-4"
                  options={menuCategories.map((cat) => ({
                    value: cat.id,
                    label: cat.title,
                  }))}
                  onChange={handleMenuCategoryChange} 
                  value={
                    menuCategories.find(
                      (cat) =>
                        cat.id === formik.values.menuCategoryId
                    )
                      ? {
                          value: formik.values.menuCategoryId!,
                          label: menuCategories.find(
                            (cat) =>
                              cat.id ===
                              formik.values.menuCategoryId
                          )!.title,
                        }
                      : null
                  }
                />
                {formik.touched.menuCategoryId &&
                  formik.errors.menuCategoryId && (
                    <small className="text-danger">
                      {formik.errors.menuCategoryId}
                    </small>
                  )}
              </Col>

              <Col md="6">
                <h4>{translate("product.selectMenuSubCategories")}</h4>
                <Select
                  className="mb-4"
                  options={subMenuCategories.map((cat) => ({
                    value: cat.id,
                    label: cat.title,
                  }))}
                  onChange={(selectedOption) =>
                    formik.setFieldValue(
                      "subMenuCategoryId",
                      selectedOption ? selectedOption.value : null
                    )
                  }
                  value={
                    subMenuCategories.find(
                      (cat) =>
                        cat.id === formik.values.subMenuCategoryId
                    )
                      ? {
                          value: formik.values.subMenuCategoryId!,
                          label: subMenuCategories.find(
                            (cat) =>
                              cat.id ===
                              formik.values.subMenuCategoryId
                          )!.title,
                        }
                      : null
                  }
                />
                {formik.touched.subMenuCategoryId &&
                  formik.errors.subMenuCategoryId && (
                    <small className="text-danger">
                      {formik.errors.subMenuCategoryId}
                    </small>
                  )}
              </Col>
            </Row> */}


            <Row>
              <Col md={`${["TAKE_HOME_TREAT"].includes(selectedCategoryType) ? "6" : "12"}`}>
                <FloatingLabel controlId="menuCategoryId" label={translate("product.selectMenuCategories")} className="mb-3">
                  <Form.Select
                    name="menuCategoryId"
                    value={formik.values.menuCategoryId}
                    onChange={(e) => handleMenuCategoryChange({ value: e.target.value })}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.menuCategoryId && !!formik.errors.menuCategoryId}
                  >
                    <option value={0} key={0}>Select</option>
                    {menuCategories.map((cat) => (
                      <option value={cat.id} key={cat.title}>
                        {cat.title}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.menuCategoryId && formik.errors.menuCategoryId}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              {/* {
                ["TAKE_HOME_TREAT"].includes(selectedCategoryType) && 
              <Col md="6" className="d-flex align-items-center">
                <Form.Check
                  type="checkbox"
                  id="needAssistant"
                  name="needAssistant"
                  label={translate("product.needAssist")}
                  checked={formik.values.needAssistant}
                  onChange={formik.handleChange}
                />
                <div className="d-flex align-items-center ms-2">
                  <FaInfoCircle
                    className="info-icon"
                    data-tooltip-id="needAssistant-tooltip"
                    data-tooltip-content={`${translate("product.needAssistInfo")}`}
                  />
                  <Tooltip
                    id="needAssistant-tooltip"
                    place="top"
                  />
                </div>
              </Col>
               } */}
              {/* {
                subMenuCategories?.length > 0 &&
                <Col md="12">
                  <FloatingLabel controlId="subMenuCategoryId" label={translate("product.selectMenuSubCategories")} className="mb-3">
                    <Form.Select
                      name="subMenuCategoryId"
                      value={formik.values.subMenuCategoryId}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      isInvalid={formik.touched.subMenuCategoryId && !!formik.errors.subMenuCategoryId}
                    >
                      <option value={0} key={0}>Select</option>
                      {subMenuCategories.map((cat) => (
                        <option value={cat.id} key={cat.title}>
                          {cat.title}
                        </option>
                      ))}
                    </Form.Select>
                    <Form.Control.Feedback type="invalid">
                      {formik.touched.subMenuCategoryId && formik.errors.subMenuCategoryId}
                    </Form.Control.Feedback>
                  </FloatingLabel>
                </Col>
              } */}
            </Row>

            {
              formik?.values?.isSuggested &&
              <Row>
                <Col md="12">
                  <h4>{translate("product.selectSuggestionProduct")}</h4>
                  <Select
                    className="mb-4"
                    isMulti
                    options={suggestionProducts.map((sp) => ({
                      value: sp.id,
                      label: sp.title,
                    }))}
                    onChange={(selectedOptions) =>
                      formik.setFieldValue(
                        "suggestionProduct",
                        selectedOptions ? selectedOptions.map(option => option.value) : []
                      )
                    }
                    value={suggestionProducts
                      .filter((sp) =>
                        (formik.values.suggestionProduct || []).includes(sp.id)
                      )
                      .map((sp) => ({
                        value: sp.id,
                        label: sp.title,
                      }))}
                  />
                  {formik.touched.suggestionProduct &&
                    Array.isArray(formik.errors.suggestionProduct) &&
                    formik.errors.suggestionProduct.length > 0 && (
                      <div>
                        {formik.errors.suggestionProduct.map((error: any, index) => (
                          <small key={index} className="text-danger">
                            {error}
                          </small>
                        ))}
                      </div>
                    )}

                </Col>
                {formik.touched.suggestionProduct && formik.errors.suggestionProduct && (
                  <small className="text-danger">
                    {Array.isArray(formik.errors.suggestionProduct)
                      ? formik.errors.suggestionProduct.join(', ')
                      : typeof formik.errors.suggestionProduct === 'string'
                        ? formik.errors.suggestionProduct
                        : 'Invalid error format'}
                  </small>
                )}
              </Row>
            }

            <Row>
              <Col md="12">
                <h4>{translate("product.selectCustomizations")} </h4>
                <CustomizationSelect
                  options={customizationOptions}
                  onChange={handleCustomizationSelection}
                />

                {typeof formik.errors.productCustomizations === "string" && (
                  <div className="text-danger">
                    {formik.errors.productCustomizations}
                  </div>
                )}

                {selectedCustomization.map((acc, index) => {

                  return (
                    <CategoryAccordion
                      key={acc.id}
                      acc={acc}
                      index={index}
                      itemsList={itemsList}
                      items={items[acc.value] || []}
                      handleCustomization={handleCustomization}
                      handleQuantityAddedChange={handleQuantityAddedChange}
                      handleCheckboxChange={handleCheckboxChange}
                      formik={formik}
                      updateCustomizationsItems={updateCustomizationsItems}
                    />
                  )
                })}

                {/* {selectedCustomization.map((acc, index) => (
                  <Accordion key={acc.id}>
                    <Accordion.Item eventKey={acc.value}>
                      <Accordion.Header>{acc.label}</Accordion.Header>
                      <Accordion.Body>
                        <Row>
                          <Col md={3} className="d-flex align-items-center">
                            <Form.Label className="mb-0">
                              {translate("product.isRequired")}
                            </Form.Label>
                            <Form.Check
                              className="ms-2"
                              type="checkbox"
                              checked={
                                formik.values.productCustomizations.find(
                                  (customization) => customization.id === acc.id
                                )?.isRequired || false
                              }
                              onChange={(e) =>
                                handleCheckboxChange(
                                  acc.id,
                                  e.target.checked,
                                  index
                                )
                              }
                            />
                          </Col>

                          <Col md={3} className="d-flex align-items-center">
                            <Form.Label>
                              <p>{translate("product.maxQuantity")}</p>
                            </Form.Label>
                            <Form.Control
                              type="number"
                              inputMode="numeric"
                              pattern="[0-9]*"
                              value={
                                // Get the current maxItems value from Formik's state
                                formik.values.productCustomizations.find((c) => c.id === acc.id)?.maxItems || ""
                              }
                              onChange={(e) => {
                                const value = Number(e.target.value.replace(/\D/g, ''));

                                // Update Formik's state
                                const updatedCustomization = formik.values.productCustomizations.find(
                                  (c) => c.id === acc.id
                                );

                                if (updatedCustomization) {
                                  const updatedCustomizationWithMaxItems = {
                                    ...updatedCustomization,
                                    maxItems: value,
                                  };

                                  const index = formik.values.productCustomizations.findIndex(
                                    (c) => c.id === acc.id
                                  );

                                  updateCustomizationsItems(updatedCustomizationWithMaxItems, index);
                                }
                              }}
                              className="ms-2"
                              style={{ width: "120px" }}
                            />
                          </Col>

                        </Row>
                        <div className="items-selections">
                          <h4>
                            {translate("product.pleaseSelectCustomizations")}
                          </h4>
                          <Select
                            isMulti
                            className="mb-4"
                            options={itemsList}
                            onChange={(selected) =>
                              handleCustomization(selected, acc, index)
                            }
                            value={
                              items[acc.value]?.map((item) => ({
                                value: item.value,
                                label: item.label,
                              })) || []
                            }
                          />
                          <ItemList
                            items={items[acc.value] || []}
                            handleAdded={(itemId, isAddedValue) =>
                              handleQuantityAddedChange(
                                acc.id,
                                itemId,
                                isAddedValue
                              )
                            }
                          />
                        </div>
                        {formik.errors.productCustomizations &&
                          Array.isArray(
                            formik.errors.productCustomizations
                          ) && (
                            <div className="text-danger">
                              {formik.errors?.productCustomizations &&
                                Array.isArray(
                                  formik.errors.productCustomizations
                                ) &&
                                formik.errors.productCustomizations[index] && (
                                  <div className="text-danger">
                                    {Object.keys(
                                      formik.errors.productCustomizations[
                                      index
                                      ] as any
                                    ).map((key) => (
                                      <div key={key}>
                                        {
                                          (
                                            formik.errors
                                              ?.productCustomizations?.[
                                            index
                                            ] as any
                                          )[key]
                                        }
                                      </div>
                                    ))}
                                  </div>
                                )}
                            </div>
                          )}
                      </Accordion.Body>
                    </Accordion.Item>
                  </Accordion>
                ))} */}
              </Col>
            </Row>
            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default ProductAdd;
