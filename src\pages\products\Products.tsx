import AddActionComponent from "components/AddActionComponent";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import ImageModal from "components/ImageModal";
import SearchInputComponent from "components/SearchInputComponent";
import StatusActionComponent from "components/StatusActionComponent";
import StatusComponent from "components/StatusComponent";
import useTranslate from "hooks/useTranslate";
import { CustomizationInterface, ItemInterface } from "interfaces";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { productService } from "services";
import BreadcrumbComponent from "components/Breadcrumb";
// import { productListBreadcrumbs } from "constants/breadcrums";
import ViewActionComponent from "components/ViewActionComponent";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import DeleteActionComponent from "components/DeleteActionComponent";
import DeleteModal from "components/Modal/DeleteModal";
import { toast } from "react-toastify";

const Products = () => {

  const { translate } = useTranslate();
  const [data, setData] = useState<CustomizationInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");
  const [show, setShow] = useState<any>(false);
  const [id, setId] = useState<any>("");
  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);


  const updateStatus = async (id: number) => {
    try {
      const response: any = await productService.updateStatus(id)
      if (response?.status === 200) {
        setData(prevData =>
          prevData.map(product =>
            product.id === id ? { ...product, isSuspended: !product.isSuspended } : product
          )
        );
      }
    } catch (error) { }

  };



  const removeRecord = async (id: number | string) => {
    setId(id)
    handleShow()

  };

  const renderActionColumn = (row: ItemInterface) => {
    return (
      <>
        <StatusActionComponent
          id={Number(row.id)}
          // key={Number(row.id)}
          isSuspended={Boolean(row.isSuspended)}
          updateStatus={updateStatus}
        />{" "}

        <EditActionComponent url={`/products/${row.id}/edit`}></EditActionComponent>{" "}

        <ViewActionComponent url={`/products/${row.id}/view`} /> {" "}
        <DeleteActionComponent fn={() => removeRecord(Number(row.id))} />{" "}
      </>
    );
  };
  const fetchData = async () => {
    setLoading(true);
    const payload = {
      page,
      pagination: true,
      skip: (page - 1) * perPage,
      limit: perPage,
      sortBy,
      sortDirection,
      searchTerm,
      activeStatus: "ALL",
    };
    try {
      const response = await productService.list(payload);
      setData(response.data.data.docs);
      setTotalRows(response.data.data.meta.totalDocs);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: any) => {
    try {
      await productService.deleteProduct(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData => prevData.filter(staff => staff.id !== id));
    } catch (error) {
      console.error(error);
    }
    handleClose(); // Close the modal after delete
  };

  const renderCategoryStatus = (isSuspended: boolean) => {
    return <StatusComponent isSuspended={!isSuspended} />;
  };

  const columns = [
    {
      name: translate("product.title"),
      sortable: true,
      sortField: "title",
      selector: (row: CustomizationInterface) => row.title || "N/A",
    },
    {
      name: translate("product.image"),
      sortable: false,
      sortField: "image",
      selector: (row: CustomizationInterface) => <ImageModal height={"20px"} thumbnailSrc={row.image} altText={row.title} />,
    },
    {
      name: translate("product.active"),
      sortable: false,
      sortField: "isSuspended",
      selector: (row: CustomizationInterface) => renderCategoryStatus(!row.isSuspended),
    },
    {
      name: translate("product.action"),
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [page, perPage, sortBy, sortDirection, searchTerm]);

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/products", entities: "Products" })} />

      {/* <BreadcrumComponent breadcrumbs={productListBreadcrumbs} /> */}
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center custom-card-header-title">
              <FaList />
              <h4 className="mb-0">{translate("product.page-title")}</h4>
            </div>
            <div>
              <AddActionComponent url="/products/add" />
            </div>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex gap-3 pb-4 search-box">
            <SearchInputComponent
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          <DatatableComponent
            columns={columns}
            data={data}
            loading={loading}
            totalRows={totalRows}
            setPage={setPage}
            setPerPage={setPerPage}
            setSortBy={setSortBy}
            setSortDirection={setSortDirection}
          />

          <DeleteModal title="Product" id={id} show={show} handleDelete={handleDelete} handleClose={handleClose} />
        </Card.Body>
      </Card>
    </>
  );
};

export default Products;