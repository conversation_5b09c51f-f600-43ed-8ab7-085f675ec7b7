import React, { useEffect, useState } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import ImageUploadComponent from "components/ImageUploadComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { staffService } from "services";
import { StaffInterface } from "interfaces";
import { useNavigate, useParams } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import PermissionsComponent from "./Permission";
import Module from "module";

// Helper function to create validation schema
const createValidationSchema = (isNewStaff: boolean) =>
  yup.object().shape({
    name: yup
      .string()
      .trim()
      .required("Name is required")
      .min(3, "Name must be at least 3 characters")
      .max(50, "Name cannot exceed 50 characters"),

    email: yup.string().email("Invalid email").required("Email is required"),

    password: isNewStaff ? yup
      .string()
      .required("Please enter a password")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])(?=.{8,})/,
        "Must contain 8 characters, one uppercase, one lowercase, one number, and one special case character"
      )
      : yup.string(),

    countryCode: yup.string().required("Country code is required"),

    phone: yup.string().trim().required("Phone number is required"),

    avatar: yup.string().required("Avatar is required"),
  });

// Main component
const AddStaff: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const [showPassword, setShowPassword] = useState(false);

  const [initialValues, setInitialValues] = useState({
    name: "",
    email: "",
    countryCode: "+91",
    phone: "",
    avatar: "",
    password: "",
    permission: ""
  });
  const [permission, setPermission] = useState<Module>()
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  const isNewStaff = !id;

  const fetchStaff = async (id: string) => {
    try {
      const {
        data: { data: { staff } },
      } = await staffService.getDetail(id);
      setInitialValues({ name: staff.restaurantName, permission: staff?.permission?.split(','), ...staff });
      setSelectedPermissions(staff?.permission?.split(','))
    } catch (error) {
      console.error("Error fetching staff:", error);
      toast.error("Failed to fetch staff data");
    }
  };
  const fetchPermission = async () => {
    const permission = await staffService.permissionList();
    setPermission(permission?.data?.data?.permissions)
  }

  useEffect(() => {
    if (id) {
      fetchStaff(id);
    }
    fetchPermission()
  }, [id]);

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: createValidationSchema(isNewStaff),
    onSubmit: async (values) => {
      try {
        if (id) {
          await staffService.update(id, {
            name: values.name,
            email: values.email,
            phone: values.phone,
            countryCode: values.countryCode,
            avatar: values.avatar,
            permission: values.permission
          });
          toast.success("Staff updated successfully");
        } else {
          await staffService.add(values as StaffInterface);
          toast.success("Staff added successfully");
        }
        navigate("/staffs");
      } catch (err) {
        console.error("Error submitting form:", err);
        toast.error("Failed to submit form");
      }
    },
  });



  const handlePermissionsChange = (permissions: string[]) => {
    setSelectedPermissions(permissions);
    formik.setFieldValue('permission', permissions.join(','))
  };


  return (

    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs(id ? { isEdit: true, entityPath: "/staffs", entities: "Staffs" } : { isAdd: true, entityPath: "/staffs", entities: "Staffs" })} />
      <Card className="custom-card">
        <Card.Header>
          <h4 className="mb-0">{id ? translate("staff.edit") : translate("staff.add")}</h4>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <FloatingLabel
                  controlId="name"
                  label={translate("staff.name")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.name && !!formik.errors.name}
                    autoComplete="off"
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.name && formik.errors.name}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="email"
                  label={translate("staff.email")}
                  className="mb-3"
                >
                  <Form.Control
                    type="email"
                    name="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.email && !!formik.errors.email}
                    autoComplete="off"
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.email && formik.errors.email}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              {isNewStaff && <Col md="6">
                <FloatingLabel
                  controlId="password"
                  label={translate("staff.password")}
                  className="mb-3"
                >
                  <Form.Control
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formik.values.password}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={
                      formik.touched.password && !!formik.errors.password
                    }
                    autoComplete="off"
                  />
                  <Button
                    className="eye-icon"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.password && formik.errors.password}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              }

              <Col md="6">
                <FloatingLabel
                  controlId="phone"
                  label={translate("staff.phone")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="phone"
                    value={formik.values.phone}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, "");
                      formik.setFieldValue("phone", value);
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.phone && !!formik.errors.phone}
                    autoComplete="off"
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.phone && formik.errors.phone}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="12">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("staff.upload-image")}</Form.Label>
                  <ImageUploadComponent
                    bucketType="staffs"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.avatar}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("avatar", "")}
                    fieldName="avatar"
                  />
                  {formik.touched.avatar && formik.errors.avatar && (
                    <small className="text-danger">
                      {formik.errors.avatar}
                    </small>
                  )}
                </Form.Group>
                <PermissionsComponent permissionsList={permission} selectedPermissions={selectedPermissions} onChange={handlePermissionsChange} />
              </Col>

              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default AddStaff;
