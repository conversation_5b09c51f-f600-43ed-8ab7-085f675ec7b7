import logo from "assets/images/logo.png";
import { useFormik } from "formik";
import { VerifyOtpInterface } from "interfaces";
import { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Container,
  FloatingLabel,
  Form,
  Image,
  Row,
} from "react-bootstrap";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { authService } from "services";
import * as yup from "yup";

const VerifyOtp = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const email = queryParams.get("email") || "";
  const type = queryParams.get("type");
  const verificationType = queryParams.get("verificationType");

  const formik = useFormik({
    initialValues: {
      otp: "",
      email,
      type,
      verificationType
    },
    validationSchema: yup.object().shape({
      otp: yup
        .string()
        .required("Please enter otp"),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      const payload = { ...values };
      try {
        const response = await authService.verifyOtp(payload);
        setSubmitting(false);
        if (response.data.success) {
          toast.success(response?.data?.message);
          navigate(`/reset-password?email=${payload.email}`)
        } else {
          toast.warning(response?.data?.message);
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  const showError = <T extends keyof VerifyOtpInterface>(field: T) =>
    formik.touched[field] && formik.errors[field] ? (
      <span className="text-danger">{formik.errors[field] as ReactNode}</span>
    ) : null;

  const handleResendOtpClick = async (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();
    const payload = { email, type, verificationType };
    try {
      const response = await authService.requestOtp(payload);
      if (response.data.success) {
        toast.success(response?.data?.message);
      } else {
        toast.warning(response?.data?.message);
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <Container>
      <Row>
        <div
          className="card-box d-flex justify-content-center align-items-center "
          style={{ minHeight: "100vh" }}
        >
          <Card className="bg-transparent">
            <Card.Body>
              <div className="text-center">
                <Image src={logo} alt="logo" className="card-box-logo" />
              </div>
              <Card.Title className="text-center mb-3">
                Verify Otp
              </Card.Title>
              <Card.Subtitle className="text-center mb-3">
                Enter otp here
              </Card.Subtitle>
              <Form>
                <FloatingLabel controlId="otp" label="OTP" className="mb-3">
                  <Form.Control
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={formik.values.otp}
                    maxLength={4}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      // Filter out non-digit characters
                      if (/^\d*$/.test(newValue)) {
                        formik.setFieldValue('otp', newValue);
                      }
                    }}
                    onBlur={formik.handleBlur}
                  />
                  {showError("otp")}
                </FloatingLabel>

                <div className="text-center resendOtp">
                  <Link to="#" onClick={handleResendOtpClick}>
                    Resend OTP
                  </Link>
                </div>

                <Button
                  variant="primary"
                  type="submit"
                  onClick={(e) => {
                    e.preventDefault();
                    formik.handleSubmit();
                  }}
                  className="mb-3 card-box-btn"
                  disabled={formik.isSubmitting}
                >
                  {formik.isSubmitting ? "Submitting..." : "Submit"}
                </Button>
                <div className="text-center">
                  <Link to="/">Back to login</Link>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </div>
      </Row>
    </Container>
  );
};

export default VerifyOtp;
