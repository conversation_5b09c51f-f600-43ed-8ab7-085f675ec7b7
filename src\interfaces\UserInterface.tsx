import { Gender, Language } from "enums";

export interface UserInterface {
  id: number;
  avatar: string;
  companyName: string;
  fullName: string;
  email: string;
  password: string;
  countryCode: string;
  countrySortName: string;
  phone: string;
  formattedPhone: string;
  gender: Gender;
  language: Language;
  deviceToken: string;
  pushNotificationAllowed: boolean;
  isPhoneVerified: boolean;
  isEmailVerified: boolean;
  isSuspended: boolean;
  isDeleted: boolean;
  authTokenIssuedAt: Date;
  created: Date;
  updated: Date;
}
