import Swal, { <PERSON><PERSON>lertIcon, Sweet<PERSON>lertResult } from 'sweetalert2';

class alertService {
  static showSuccess(title?: string, text?: string): void {
    Swal.fire({
      title: title || 'Success!',
      text: text || 'Operation was successful.',
      icon: 'success' as <PERSON><PERSON>lertIcon,
      confirmButtonText: 'OK'
    });
  }

  static showError(title?: string, text?: string): void {
    Swal.fire({
      title: title || 'Error!',
      text: text || 'Something went wrong.',
      icon: 'error' as SweetAlertIcon,
      confirmButtonText: 'OK'
    });
  }

  static showWarning(title?: string, text?: string): void {
    Swal.fire({
      title: title || 'Warning!',
      text: text || 'Please be careful.',
      icon: 'warning' as SweetAlertIcon,
      confirmButtonText: 'OK'
    });
  }

  static showInfo(title?: string, text?: string): void {
    Swal.fire({
      title: title || 'Information',
      text: text || 'Here is some information.',
      icon: 'info' as SweetAlertIcon,
      confirmButtonText: 'OK'
    });
  }

  static async showConfirmation(title?: string, text?: string): Promise<SweetAlertResult> {
    return Swal.fire({
      title: title || 'Are you sure?',
      text: text || 'You won\'t be able to revert this!',
      icon: 'warning' as SweetAlertIcon,
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
    });
  }
}

export default alertService;
