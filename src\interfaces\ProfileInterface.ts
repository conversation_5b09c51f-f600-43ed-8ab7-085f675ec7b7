// Define interface for user profile data
export interface UserProfile {
  id?: number;
  role?: string;
  avatar: string;
  restaurantName: string;
  email: string;
  password?: string;
  countryCode?: string;
  countrySortName?: string;
  phone: string
  formattedPhone?: string
  address: string
  city: string
  latitude: number
  longitude: number
  // restaurantDoc: unknown;
  language?: string;
  totalRating?: number;
  avgRating?: number;
  deviceToken?: null | string;
  pushNotificationAllowed?: number;
  isPhoneVerified?: number;
  isEmailVerified?: number;
  verificationStatus?: string;
  isSuspended?: number;
  isDeleted?: number;
  authTokenIssuedAt?: string
  created?: string
  updated?: string
  restaurantId?: null | number;
}
