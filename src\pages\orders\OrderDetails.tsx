import ImageModal from "components/ImageModal";
import StatusComponent from "components/StatusComponent";
import useTranslate from "hooks/useTranslate";
import { useEffect, useState } from "react";
import { Card, Col, Row } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import { orderService } from "services/order.service";
import './style.css'

const OrderDetails = () => {
    const { translate } = useTranslate();
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const [orderDetail, setOrderDetails] = useState<any>(null);


    const fetchOrderDetails = async () => {
        try {
            const response = await orderService.getDetail(Number(id));
            setOrderDetails(response.data.data.order);
        } catch (error) {
            console.error("Error fetching product details:", error);
        }
    };

    useEffect(() => {
        fetchOrderDetails()
    }, [id]);

    return (
        <>
            {/* <BreadcrumbComponent breadcrumbs={orderDetailBreadcrumbs} /> */}
            <Card className="custom-card">
                <Card className="custom-card order-details">
                    <Card.Header>
                        <div className="d-flex justify-content-between align-items-center">
                            <h4 className="mb-0 order-details-title">{translate("product.details")}</h4>
                        </div>
                    </Card.Header>
                    <Card.Body>
                        <div className="flex-column align-items-start">
                            <h5 className="mb-4 order-id">Order Id: {orderDetail?.id || "N/A"}</h5>
                            <Row>
                                <Col md="6" className="mb-4">
                                    <div className="d-flex gap-3">
                                        <div className="order-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M4.00488 16V4H2.00488V2H5.00488C5.55717 2 6.00488 2.44772 6.00488 3V15H18.4433L20.4433 7H8.00488V5H21.7241C22.2764 5 22.7241 5.44772 22.7241 6C22.7241 6.08176 22.7141 6.16322 22.6942 6.24254L20.1942 16.2425C20.083 16.6877 19.683 17 19.2241 17H5.00488C4.4526 17 4.00488 16.5523 4.00488 16ZM6.00488 23C4.90031 23 4.00488 22.1046 4.00488 21C4.00488 19.8954 4.90031 19 6.00488 19C7.10945 19 8.00488 19.8954 8.00488 21C8.00488 22.1046 7.10945 23 6.00488 23ZM18.0049 23C16.9003 23 16.0049 22.1046 16.0049 21C16.0049 19.8954 16.9003 19 18.0049 19C19.1095 19 20.0049 19.8954 20.0049 21C20.0049 22.1046 19.1095 23 18.0049 23Z"></path></svg>
                                        </div>
                                        <div className="d-flex flex-column gap-2">
                                            <h6 className="order-title mb-0">Order Info</h6>
                                            <p className="order-details mb-0"><b>Order Name:</b> {orderDetail?.orderName || "N/A"}</p>
                                            <p className="order-details mb-0"><b>Order Type:</b> {orderDetail?.orderType || "N/A"}</p>
                                            <p className="order-details mb-0"><b>Status:</b> {orderDetail?.status} </p>
                                        </div>
                                    </div>
                                </Col>

                                <Col md="6" className="mb-4">
                                    <div className="d-flex gap-3">
                                        <div className="order-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20.0049 2C21.1068 2 22 2.89821 22 3.9908V20.0092C22 21.1087 21.1074 22 20.0049 22H4V18H2V16H4V13H2V11H4V8H2V6H4V2H20.0049ZM8 4H6V20H8V4ZM20 4H10V20H20V4Z"></path></svg>
                                        </div>
                                        <div className="d-flex flex-column gap-2">
                                            <h6 className="order-title mb-0">Notes</h6>
                                            <p className="order-details mb-0"><b>Message on Cake:</b> {orderDetail?.messageOnCake || "N/A"}</p>
                                            <p className="order-details mb-0"><b>Need Candles:</b> {orderDetail?.needCandle ? 'Yes Need Candles' : 'No Not Needed'}</p>
                                            <p className="order-details mb-0"><b>Allergies Instructions:</b> {orderDetail?.allergiesInstructions || "N/A"} </p>
                                        </div>
                                    </div>
                                </Col>
                                <Col md="6" className="mb-4">
                                    <div className="d-flex gap-3">
                                        <div className="order-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M14.2683 12.1466L13.4147 13.0002L20.4858 20.0712L19.0716 21.4854L12.0005 14.4144L4.92946 21.4854L3.51525 20.0712L12.854 10.7324C12.2664 9.27549 12.8738 7.17715 14.4754 5.57554C16.428 3.62292 19.119 3.14805 20.4858 4.51488C21.8526 5.88172 21.3778 8.57267 19.4251 10.5253C17.8235 12.1269 15.7252 12.7343 14.2683 12.1466ZM4.22235 3.80777L10.9399 10.5253L8.11144 13.3537L4.22235 9.46463C2.66026 7.90253 2.66026 5.36987 4.22235 3.80777ZM18.0109 9.11107C19.2682 7.85386 19.5274 6.38488 19.0716 5.92909C18.6158 5.47331 17.1468 5.73254 15.8896 6.98975C14.6324 8.24697 14.3732 9.71595 14.829 10.1717C15.2847 10.6275 16.7537 10.3683 18.0109 9.11107Z"></path></svg>
                                        </div>
                                        <div className="d-flex flex-column gap-2">
                                            <h6 className="order-title mb-0">Items Details</h6>
                                            {orderDetail?.orderProducts.map((order: any) => {
                                                return (<>
                                                    <p className="order-details mb-0"><b>Image:</b> <ImageModal
                                                        thumbnailSrc={order.image}
                                                        altText={order.title}
                                                    /></p>
                                                    <p className="order-details mb-0"><b>Item Name:</b> {order.title}</p>
                                                    <p className="order-details mb-0"><b>Quantity:</b> {order.quantity} </p>
                                                </>)
                                            })}
                                        </div>
                                    </div>
                                </Col>
                                <Col md="6" className="mb-4">
                                    <div className="d-flex gap-3">
                                        <div className="order-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3.00488 2.99979H21.0049C21.5572 2.99979 22.0049 3.4475 22.0049 3.99979V19.9998C22.0049 20.5521 21.5572 20.9998 21.0049 20.9998H3.00488C2.4526 20.9998 2.00488 20.5521 2.00488 19.9998V3.99979C2.00488 3.4475 2.4526 2.99979 3.00488 2.99979ZM20.0049 10.9998H4.00488V18.9998H20.0049V10.9998ZM20.0049 8.99979V4.99979H4.00488V8.99979H20.0049ZM14.0049 14.9998H18.0049V16.9998H14.0049V14.9998Z"></path></svg>
                                        </div>
                                        <div className="d-flex flex-column gap-2">
                                            <h6 className="order-title mb-0">Payment Info</h6>
                                            <p className="order-details mb-0"><b>Total:</b> {orderDetail?.total}</p>
                                            <p className="order-details mb-0"><b>Service Charge:</b> {orderDetail?.serviceCharge}</p>
                                            <p className="order-details mb-0"><b>Tax:</b> {orderDetail?.tax} </p>
                                            <p className="order-details mb-0"><b>Tip:</b> {orderDetail?.tip} </p>
                                            <p className="order-details mb-0"><b>Total Amount:</b> {orderDetail?.totalAmount} </p>
                                        </div>
                                    </div>
                                </Col>
                            </Row>
                        </div>

                    </Card.Body>
                </Card>
            </Card>
        </>
    )
}
export default OrderDetails;