import { UserProfile } from "interfaces/ProfileInterface";
import base from "./base.service";


const getProfile = () => base.get("/my-account/profile");
const updateProfile = (payload: UserProfile) => base.put("/my-account/profile", payload);
// const deleteAccount = () => base.delete("my-account/profile");
const changePassword = (payload: any) => base.put("/my-account/change-password", payload);
const logout = () => base.get("/my-account/logout");
const syncSquareData = () => base.get("/my-account/sync-square-data");

export const myAccountService = {
  getProfile,
  updateProfile,
  // deleteAccount,
  changePassword,
  logout,
  syncSquareData
};
