import AuthLayout from "containers/AuthLayout";
import MainLayout from "containers/MainLayout";
import ProtectedLayout from "containers/ProtectedLayout";
import ProductAdd from "pages/products/AddProduct";
import ChangePassword from "pages/auth/ChangePassword";
import Customizations from "pages/customizations/Customizations";
import Dashboard from "pages/Dashboard";
import ForgotPassword from "pages/auth/ForgotPassword";
import Login from "pages/auth/Login";
import Menus from "pages/menuCategories/Menus";
import AddMenu from "pages/menuCategories/AddMenu";
import NotFoundPage from "pages/NotFoundPage";
import Products from "pages/products/Products";
import Profile from "pages/auth/Profile";
import ResetPassword from "pages/auth/ResetPassword";
import Settings from "pages/Settings";
import { Route, Routes } from "react-router-dom";
import Items from "pages/Items/Items";
import AddItem from "pages/Items/AddItem";
import UpdateItem from "pages/Items/UpdateItem";
import ItemDetail from "pages/Items/Detail";
import UpdateProduct from "pages/products/UpdateProduct";
import ProductDetail from "pages/products/Detail";
import VerifyOtp from "pages/auth/VerifyOtp";
import MenuDetail from "pages/menuCategories/Detail";
import UpdateMenu from "pages/menuCategories/UpdateMenu";
import Staffs from "pages/staffs/Staffs";
import StaffDetail from "pages/staffs/Detail";
import AddStaff from "pages/staffs/AddStaff";
import Customers from "pages/customers/Customers";
import CustomerDetail from "pages/customers/Detail";
import AddCustomer from "pages/customers/AddCustomer";
import Orders from "pages/orders/Orders";
import CateringList from "pages/catering/CateringList";
import Transactions from "pages/transactions/Transactions";
import NotificationList from "pages/notifications/NotificationList";
import OrderDetails from "pages/orders/OrderDetails";
import AddBranch from "pages/BranchManagement/AddBranch";
import Branches from "pages/BranchManagement/Bracnhes";
import SalesReport from "pages/SalesReport/SalesReport";
import StaticPages from "pages/StaticPages/List";
import AddStaticPage from "pages/StaticPages/AddPage";
import AddCustomizaion from "pages/customizations/AddCustomization";
import AddMenuLayout from "pages/menuLayout/AddMenuLayout";
import MenusLayoutList from "pages/menuLayout/MenusLayoutList";
import UpdateMenuLayout from "pages/menuLayout/UpdateMenuLayout";
import PromoCodeList from "pages/promoCode/PromoCodeList";
import PromoCodeDetailList from "pages/promoCode/PromoCodeDetailList";
import AddPromoCode from "pages/promoCode/AddPromoCode";
import UpdatePromoCode from "pages/promoCode/UpdatePromoCode";



export function Routing() {
  return (
    <Routes>
      <Route path="/" element={<MainLayout />}>
        <Route element={<AuthLayout />}>
          <Route path="/" element={<Login />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/verify-otp" element={<VerifyOtp />} />
          <Route path="/reset-password" element={<ResetPassword />} />
        </Route>
        <Route element={<ProtectedLayout />}>
          <Route path="/profile" element={<Profile />} />
          <Route path="/change-password" element={<ChangePassword />} />
          <Route path="/settings" element={<Settings />} /> {/* still in process*/}
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/categories" element={<Menus />} />
          <Route path="/categories/add" element={<AddMenu />} />
          <Route path="/categories/:id/view" element={<MenuDetail />} />
          <Route path="/categories/:id/edit" element={<UpdateMenu />} />
          <Route path="/customizations" element={<Customizations />} />
          <Route path="/customization/add" element={<AddCustomizaion />} />
          <Route path="/customization/:id/edit" element={<AddCustomizaion />} />

          <Route path="/items" element={<Items />} />
          <Route path="/items/add" element={<AddItem isEdit={false} />} />
          <Route path="/items/:id/edit" element={<AddItem isEdit={true} />} />
          <Route path="/items/:id/view" element={<ItemDetail />} />

          <Route path="/products" element={<Products />} />
          <Route path="/products/add" element={<ProductAdd />} />
          <Route path="/products/:id/edit" element={<UpdateProduct />} />
          <Route path="/products/:id/view" element={<ProductDetail />} />

          <Route path="/staffs" element={<Staffs />} />
          <Route path="/staffs/:id/view" element={<StaffDetail />} />
          <Route path="/staffs/add" element={<AddStaff />} />
          <Route path="/staffs/:id/edit" element={<AddStaff />} />

          {/* menu-layouts */}
          <Route path="/menu-layout" element={<MenusLayoutList />} />
          <Route path="/menu-layout/:id/view" element={<StaffDetail />} />
          <Route path="/menu-layout/add" element={<AddMenuLayout />} />
          <Route path="/menu-layout/:id/edit" element={<UpdateMenuLayout />} />


          <Route path="/customers" element={<Customers />} />
          <Route path="/customers/:id/view" element={<CustomerDetail />} />
          <Route path="/customers/:id/edit" element={<AddCustomer />} />
          <Route path="/customers/add" element={<AddCustomer />} />

          <Route path="/orders" element={<Orders />} />
          <Route path="/order/:id" element={<OrderDetails />} />

          <Route path="/notifications" element={<NotificationList />} />
          <Route path="/caterings" element={<CateringList />} />
          <Route path="/transactions" element={<Transactions />} />

          <Route path="/branches/add" element={<AddBranch />} />
          <Route path="/branches/edit/:id" element={<AddBranch />} />
          <Route path="/branches" element={<Branches />} />

          <Route path="/sales-report" element={<SalesReport />} />
          <Route path="/static-pages" element={<StaticPages />} />
          <Route path="/static-pages/edit/:id" element={<AddStaticPage />} />


          {/* promo-code */}
          <Route path="/promo-code" element={<PromoCodeList />} />
          <Route path="/promo-code/:id/view" element={<PromoCodeDetailList />} />
          <Route path="/promo-code/add" element={<AddPromoCode />} />
          <Route path="/promo-code/:id/edit" element={<UpdatePromoCode />} />


        </Route>
        <Route path="*" element={<NotFoundPage />} />
      </Route>
    </Routes>
  );
}
