import React, { useState } from "react";
import { Col, Form, Row } from "react-bootstrap";

interface ItemListProps {
  items: { value: string; label: string; selected?: boolean; quantity?: number, itemType?: string }[];
  handleAdded: (itemId: number, isAddedValue: boolean) => void,
  selectedCount?: number;
  maxItems?: number;
}

const ItemList: React.FC<ItemListProps> = ({
  items,
  handleAdded,
  selectedCount,
  maxItems
}) => {

  console.log(items)

  // Maintain state for checked items and their quantities
  const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>(
    items.reduce((acc, item) => {
      acc[item.value] = item.selected || false;
      return acc;
    }, {} as Record<string, boolean>)
  );

  const handleCheckboxChange = (itemValue: string, isChecked: boolean) => {
    setCheckedItems((prev) => {
      const updated = { ...prev, [itemValue]: isChecked };
      return updated;
    });

    handleAdded(itemValue as unknown as number, isChecked)
    // Apply the checkbox change
    // onQuantityChange(optionValue, itemValue, isChecked ? 1 : 0); // Adjust quantity logic if needed
  };

  console.log("items", items)

  return (
    <ul>
      {items.map((item: any) => (
        <li key={item.value} className="mt-2">
          <Row className="d-flex align-items-center">
            <Col md={3}>
              <span className="ms-2">{item.label}</span>
            </Col>
            <Col md={3}>
              <span className="ms-2">{item?.itemType}</span>
            </Col>
            {
              item?.itemType === "STANDARD" &&
              <Col md={2} className="d-flex align-items-center">
                <Form.Label className="mb-0">Is this item added ? </Form.Label>
                <Form.Check
                  className="ms-2"
                  type="checkbox"
                  checked={checkedItems[item.value] || false}
                  onChange={(e) => {
                    if (checkedItems[item.value]) {
                      // If the item is already checked, uncheck it
                      handleCheckboxChange(item.value, false);
                    }
                    if (
                      typeof maxItems === "number" &&
                      typeof selectedCount === "number" &&
                      maxItems > selectedCount
                    ) {
                      handleCheckboxChange(item.value, e.target.checked)
                    }
                  }
                  }
                />
              </Col>
            }
          </Row>
        </li>
      ))}
    </ul>
  );
};

export default ItemList;
