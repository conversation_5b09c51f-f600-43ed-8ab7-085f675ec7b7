import React, { useEffect, useState } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import ImageUploadComponent from "components/ImageUploadComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { menuService } from "services";
import { MenuCategoryInterface } from "interfaces";
import { useNavigate } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { ImagePaths } from "constants/ImagePaths";
import { FaInfoCircle } from "react-icons/fa";
import { Tooltip } from "react-tooltip";

// Helper function to create validation schema
const createValidationSchema = () =>
  yup.object().shape({
    title: yup
      .string()
      .trim()
      .required("Name is required")
      .min(3, "Name must be at least 3 characters")
      .max(50, "Name cannot exceed 50 characters"),
    image: yup.string().required("Image is required"),
    menuViewType: yup
      .string()
      .nullable()
      .when("visitFreezer", {
        is: false,
        then: (schema) => schema.required("Menu View Type is required"),
        otherwise: (schema) => schema.strip(),
      }),
    productLayoutView: yup
      .string()
      .nullable()
      .when("visitFreezer", {
        is: false,
        then: (schema) => schema.required("Product Layout View is required"),
        otherwise: (schema) => schema.strip(),
      }),
    description: yup
      .string()
      .trim()
      .required("Description is required")
      .min(3, "Description must be at least 3 characters")
      .max(200, "Description cannot exceed 200 characters"),
    parentId: yup.number().min(0, "Parent category must be selected or 0"),
    visitFreezer: yup.boolean(),
    isFeatured: yup.boolean(),
  });

const useMenuData = () => {
  const [data, setData] = useState<{
    menuCategories: MenuCategoryInterface[];
  }>({
    menuCategories: [],
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [menus] = await Promise.all([
          menuService.getParentMenuCategories({
            page: 1,
            pagination: false,
            activeStatus: "ALL",
            isParent: true,
          }),
        ]);
        setData({
          menuCategories: menus.data.data.menuCategory || [],
        });
      } catch (error) {
        console.error(error);
        toast.error("Failed to load data");
      }
    };

    fetchData();
  }, []);

  return data;
};

const AddMenu: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate();
  const { menuCategories } = useMenuData();

  const initialValues = {
    title: "",
    image: "",
    description: "",
    parentId: 0,
    visitFreezer: false,
    isFeatured: false,
    menuViewType: "SELF_VIEW",
    productLayoutView: "LAYOUT_ONE",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: createValidationSchema(),
    enableReinitialize: true,
    onSubmit: async (val) => {
      const values: any = val.visitFreezer
        ? (({ menuViewType, productLayoutView, ...rest }) => rest)(val)
        : val;

      try {
        const filteredValues = {
          ...values,
          parentId: values.parentId ? Number(values.parentId) : null,
        };
        await menuService.add(
          filteredValues as unknown as MenuCategoryInterface
        );
        toast.success("Category added successfully");
        navigate("/categories");
      } catch (err) {
        console.error("Error adding category:", err);
        toast.error("Failed to add category");
      }
    },
  });

  // Reset menuViewType and productLayoutView when visitFreezer is true
  useEffect(() => {
    if (formik.values.visitFreezer) {
      formik.setFieldValue("menuViewType", "");
      formik.setFieldValue("productLayoutView", "");
    }
  }, [formik.values.visitFreezer]);

  return (
    <>
      <BreadcrumbComponent
        breadcrumbs={getBreadcrumbs(
          formik.values.parentId
            ? { isEdit: true, entityPath: "/categories", entities: "Categories" }
            : { isAdd: true, entityPath: "/categories", entities: "Categories" }
        )}
      />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{translate("menu.add")}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <FloatingLabel controlId="title" label={translate("menu.title")} className="mb-3">
                  <Form.Control
                    type="text"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.title && !!formik.errors.title}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.errors.title}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel controlId="description" label={translate("menu.description")} className="mb-3">
                  <Form.Control
                    type="text"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.description && !!formik.errors.description}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.errors.description}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel controlId="parentId" label={translate("menu.selectMenuCategories")} className="mb-3">
                  <Form.Select
                    name="parentId"
                    value={formik.values.parentId}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.parentId && !!formik.errors.parentId}
                  >
                    <option value={0}>Select</option>
                    {menuCategories.map((cat) => (
                      <option value={cat.id} key={cat.id}>
                        {cat.title}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formik.errors.parentId}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="3" style={{ display: 'flex' }}>
                <Form.Check
                  type="switch"
                  id="visitFreezer-switch"
                  label="Visit Freezer"
                  checked={formik.values.visitFreezer}
                  onChange={(e) => formik.setFieldValue("visitFreezer", e.target.checked)}
                  className="mb-3"
                />
                <FaInfoCircle
                  data-tooltip-id="visitFreezer"
                  data-tooltip-content="This Category Items Available When Order From Restaurant"
                  className="ms-2"
                />
                <Tooltip id="visitFreezer" />
              </Col>


              <Col md="3" style={{ display: 'flex' }}>
                <Form.Check
                  type="switch"
                  id="isFeatured-switch"
                  label="Featured"
                  checked={formik.values.isFeatured}
                  onChange={(e) => formik.setFieldValue("isFeatured", e.target.checked)}
                  className="mb-3"
                />
                <FaInfoCircle
                  data-tooltip-id="isFeatured"
                  data-tooltip-content="This Category Items Available When Order From Restaurant"
                  className="ms-2"
                />
                <Tooltip id="isFeatured" />
              </Col>

              {!formik.values.visitFreezer && (
                <Col md="6">
                  <Form.Group className="mb-3">
                    <Form.Label>{translate("menu.menuViewType")}</Form.Label>
                    <div className="d-flex gap-3">
                      <Form.Check
                        type="radio"
                        name="menuViewType"
                        id="self-view"
                        label="Self View"
                        value="SELF_VIEW"
                        checked={formik.values.menuViewType === "SELF_VIEW"}
                        onChange={() => formik.setFieldValue("menuViewType", "SELF_VIEW")}
                      />
                      <div className="d-flex align-items-center ms-2">
                        <FaInfoCircle
                          className="info-icon"
                          data-tooltip-id="tooltip-self-view"
                          data-tooltip-content="User will be redirected to category detail page"
                        />
                        <Tooltip id="tooltip-self-view" place="top" />
                      </div>

                      <Form.Check
                        type="radio"
                        name="menuViewType"
                        id="parent-view"
                        label="Parent View"
                        value="PARENT_VIEW"
                        checked={formik.values.menuViewType === "PARENT_VIEW"}
                        onChange={() => formik.setFieldValue("menuViewType", "PARENT_VIEW")}
                      />
                      <div className="d-flex align-items-center ms-2">
                        <FaInfoCircle
                          className="info-icon"
                          data-tooltip-id="tooltip-parent-view"
                          data-tooltip-content="User will be redirected to parent category detail page"
                        />
                        <Tooltip id="tooltip-parent-view" place="top" />
                      </div>
                    </div>
                  </Form.Group>
                </Col>
              )}




              {!formik.values.visitFreezer && formik.values.menuViewType === "PARENT_VIEW" && (
                <Col md="12">
                  <Form.Group className="mb-3">
                    <Form.Label>{translate("menu.productLayoutView")}</Form.Label>
                    <div className="d-flex gap-4 align-items-center">
                      <Form.Check
                        type="radio"
                        name="productLayoutView"
                        id="layout-one"
                        value="LAYOUT_ONE"
                        checked={formik.values.productLayoutView === "LAYOUT_ONE"}
                        onChange={() => formik.setFieldValue("productLayoutView", "LAYOUT_ONE")}
                        label={
                          <img
                            src={ImagePaths.layout_one}
                            alt="Layout One"
                            style={{
                              width: "100%",
                              maxWidth: "500px",
                              height: "400px",
                              cursor: "pointer",
                              border:
                                formik.values.productLayoutView === "LAYOUT_ONE"
                                  ? "2px solid black"
                                  : "2px dashed black",
                              objectFit: "contain",
                            }}
                          />
                        }
                      />
                      <Form.Check
                        type="radio"
                        name="productLayoutView"
                        id="layout-two"
                        value="LAYOUT_TWO"
                        checked={formik.values.productLayoutView === "LAYOUT_TWO"}
                        onChange={() => formik.setFieldValue("productLayoutView", "LAYOUT_TWO")}
                        label={
                          <img
                            src={ImagePaths.layout_two}
                            alt="Layout Two"
                            style={{
                              width: "100%",
                              maxWidth: "500px",
                              height: "400px",
                              cursor: "pointer",
                              border:
                                formik.values.productLayoutView === "LAYOUT_TWO"
                                  ? "2px solid black"
                                  : "2px dashed black",
                              objectFit: "contain",
                            }}
                          />
                        }
                      />
                    </div>
                  </Form.Group>
                </Col>
              )}
            </Row>

            <Row className="mt-4">
              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("menu.upload-image")} (335px x 265px)</Form.Label>
                  <ImageUploadComponent
                    bucketType="menus"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.image}
                    setImage={(field, value) => formik.setFieldValue(field, value)}
                    handleRemove={() => formik.setFieldValue("image", "")}
                    fieldName="image"
                  />
                  {formik.touched.image && formik.errors.image && (
                    <small className="text-danger">{formik.errors.image}</small>
                  )}
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default AddMenu;
