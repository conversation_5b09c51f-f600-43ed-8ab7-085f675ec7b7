import base from "./base.service";
import { CustomerInterface } from "interfaces";

const getAll = (payload: any) => base.get("/user", { params: payload });
const updateStatus = (id: number | string) =>
  base.get(`/user/${id}/update-status`);
const remove = (id: number | string) => base.delete(`/user/${id}`);
const add = (payload: CustomerInterface) => base.post(`/user`, { ...payload });
const getDetail = (id: string | number) => base.get(`user/${id}`);
const update = (id: string | number, payload: any) =>
  base.put(`user/${id}`, { ...payload });

export const customerService = {
  getAll,
  updateStatus,
  remove,
  add,
  getDetail,
  update,
};
