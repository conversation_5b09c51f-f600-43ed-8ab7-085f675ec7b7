$primary: #000;
.card-header {
  padding: 1rem 1.5rem;
  margin-bottom: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeff3;
}
.no-record-found {
  display: block;
  font-size: 20px;
  padding: 50px 0;
  text-align: center;
}
.header-less-full-height {
  min-height: calc(100vh - 160px);
}
body {
  &.light-theme {
    background-color: #fff !important;
    font-family: "Open Sans", sans-serif;
    a {
      color: #000;
      text-decoration: none;
      &.breadcrumb-item {
        color: #bbbbbb;
        font-weight: 600;
        &::before {
          color: #bbbbbb;
        }
      }

      &.breadcrumb-item + .breadcrumb-item::before {
        color: #bbbbbb;
      }
    }
    .tex-primary {
      color: $primary;
    }
    .card {
      border: none;
    }
    .card-title {
      color: #000;
    }
    .card-subtitle {
      color: #444;
    }
    .card-text {
      color: #878787;
      font-size: 14px;
    }
    .card-box {
      .card {
        border: none;
      }
      .card-body {
        background: #fff;
        max-width: 450px;
        min-width: 450px;
      }
      .card-title {
        color: #000;
      }
      .card-subtitle {
        color: #878787;
      }
      .card-text {
        font-size: 20px;
        text-align: center;
        color: #878787;
      }
      &-logo {
        max-width: 120px;
        margin-bottom: 20px;
      }
      &-btn {
        width: 100%;
        text-align: center;
        background: #000;
        border: none;
        padding: 15px;
        font-weight: 600;
      }
    }
    .rmsc .dropdown-container {
      background: transparent;
      color: #bbbbbb;
      border: 1px solid #555;
      &:focus-within {
        border: 1px solid $primary;
        box-shadow: none;
      }
    }
    .dropdown-item:active {
      background: none;
    }
    .PhoneInputInput {
      background-color: transparent !important;
      border: none;
      border-radius: 0px;
      color: #bbbbbb !important;
      &:focus-visible {
        outline: none;
      }
    }
    .form-floating > .form-control:not(:placeholder-shown) ~ label::after {
      background-color: transparent;
    }
    li {
      &.breadcrumb-item.active {
        color: #000;
        font-weight: 600;
      }
    }
    .btn-primary {
      background: $primary;
      border: 1px solid $primary;
      font-weight: 600;
      &.eye-icon {
        position: absolute;
        top: 0px;
        right: 1rem;
        background: transparent;
        color: #000;
        border: none;
      }
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    input:focus-visible {
      outline: none;
    }
  }
  .react-confirm-alert {
    &-overlay {
      background: rgb(38 38 38 / 80%);
    }
    &-body {
      background: #444444;
      color: #bbbbbb;
      h1 {
        margin-top: 0;
        font-size: 34px;
        color: #fff !important;
      }
    }
  }
}
.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: black;
  color: white;
  border: 2px solid white;
  padding: 0 20px; /* Adjust horizontal padding as needed */
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
  height: 35px; /* Consistent height */
}

.upload-btn:hover {
  background-color: white;
  color: black;
  border: 2px solid rgb(0, 0, 0);
}

@media (max-width: 767px) {
  body {
    &.light-theme {
      .card-box {
        .card-body {
          min-width: 350px;
          max-width: 350px;
        }
        &-logo {
          max-width: 80px;
        }
      }
    }
  }
}

/* custom scss */

/* customAccordion.css */
.custom-accordion .accordion-button {
  background-color: black; /* Black background for the accordion header */
  color: white; /* White text color */
  border: 1px solid #444; /* Optional: border color for better separation */
}

.custom-accordion .accordion-button:not(.collapsed) {
  background-color: #333; /* Darker background for the expanded state */
  color: white; /* Keep text color white when expanded */
}

.custom-accordion .accordion-body {
  background-color: #222; /* Dark background for the body */
  color: white; /* White text color in the body */
}

.custom-accordion .accordion-header {
  border-bottom: 1px solid #444; /* Optional: border color between headers */
}

.resendOtp {
  margin-bottom: 10px;
}

.img-thumbnail {
  padding: 0;
}

.info-icon {
  cursor: pointer;
  fill: #000;
}
