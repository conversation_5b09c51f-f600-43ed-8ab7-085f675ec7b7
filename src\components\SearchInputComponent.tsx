import React, { FC, useState, useEffect, useMemo } from "react";
import { Form } from "react-bootstrap";
import _ from "lodash";

const SearchInputComponent: FC<{
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
}> = ({ searchTerm, setSearchTerm }) => {
  const [inputValue, setInputValue] = useState(searchTerm);

  const debouncedSetSearchTerm = useMemo(() => _.debounce(setSearchTerm, 500), [setSearchTerm]);

  useEffect(() => {
    debouncedSetSearchTerm(inputValue);
    // Cleanup function to cancel debounce on unmount or when inputValue changes
    return () => {
      debouncedSetSearchTerm.cancel();
    };
  }, [inputValue, debouncedSetSearchTerm]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  return <Form.Control type="text" value={inputValue} onChange={handleChange} placeholder="Search" />;
};

export default SearchInputComponent;
