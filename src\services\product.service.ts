import { ProductAddInterface } from "interfaces";
import base from "./base.service";

const list = (payload: any) => base.get(`/product`, { params: payload });

const add = (payload: ProductAddInterface) => base.post(`/product/add-product`, { ...payload });

const updateStatus = (id: string | number) => base.get(`product/${id}/update-status`);

const getDetail = (id: | string | number) => base.get(`product/detail/${id}`);

const update = (id: string | number, payload: any) => base.put(`product/update-product/${id}`, { ...payload });

const suggestionProductList = (payload: any) => base.get(`/product/suggested-product`, { params: payload });

const deleteProduct = (id: string | number) => base.delete(`product/${id}`)


export const productService = {
  list,
  add,
  updateStatus,
  getDetail,
  update,
  suggestionProductList,
  deleteProduct
};
