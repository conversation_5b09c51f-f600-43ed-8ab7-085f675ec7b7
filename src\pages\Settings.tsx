import BreadcrumbComponent from "components/Breadcrumb";
// import { settingsBreadcrumbs } from "constants/breadcrums";
import { ReactNode, useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, Col, FloatingLabel, Form, Row } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import { FaInfo } from "react-icons/fa";
import { settingService } from "services";
import { SettingInterface } from "interfaces";
import * as yup from "yup";
import { useFormik } from "formik";
import { toast } from "react-toastify";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

const Settings = () => {
  const [data, setData] = useState<SettingInterface>();
  const params = useParams();
  const navigate = useNavigate();
  useEffect(() => {
    async function fetchData() {
      const response = await settingService.get();
      if (response) {
        setData(response?.data?.data[0]);
      }
    }
    fetchData();
  }, [params.id]);

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      id: data?.id || 1,
      androidUserAppVersion: data?.androidUserAppVersion ?? "",
      iosUserAppVersion: data?.iosUserAppVersion ?? "",
      kioskUserAppVersion: data?.kioskUserAppVersion ?? "",
      androidUserAppForceUpdate: data?.androidUserAppForceUpdate
        ? "true"
        : "false",
      iosUserAppForceUpdate: data?.iosUserAppForceUpdate ? "true" : "false",
      kioskUserAppForceUpdate: data?.kioskUserAppForceUpdate
        ? "true"
        : "false",
      maintenance: data?.maintenance ? "true" : "false",
      serviceCharge: data?.serviceCharge ?? 0,
      tax: data?.tax ?? 0,
    },
    validationSchema: yup.object().shape({
      androidUserAppVersion: yup.string().required("This field is required"),
      iosUserAppVersion: yup.string().required("This field is required"),
      kioskUserAppVersion: yup.string().required("This field is required"),
      androidUserAppForceUpdate: yup
        .string()
        .required("This field is required"),
      iosUserAppForceUpdate: yup.string().required("This field is required"),
      kioskUserAppForceUpdate: yup.string().required("This field is required"),
      maintenance: yup.string().required("This field is required"),
      serviceCharge: yup.string().required("This field is required"),
      tax: yup.string().required("This field is required"),
    }),
    onSubmit: async (values) => {
      try {
        const formattedValues = {
          ...values,
          androidUserAppForceUpdate: values.androidUserAppForceUpdate === "true" ? true : false,
          iosUserAppForceUpdate: values.iosUserAppForceUpdate === "true" ? true : false,
          kioskUserAppForceUpdate: values.kioskUserAppForceUpdate === "true" ? true : false,
          maintenance: values.maintenance === "true" ? true : false,
        };

        const response = await settingService.update(formattedValues);
        if (response.data.success) {
          toast.success(response?.data?.message);
          navigate("/dashboard");
        } else {
          toast.warning(response?.data?.message);
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  const showError = <T extends keyof SettingInterface>(field: T) =>
    formik.touched[field] && formik.errors[field] ? (
      <span className="text-danger">{formik.errors[field] as ReactNode}</span>
    ) : null;

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isSingle: true, entityPath: "/settings", entities: "Settings" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <FaInfo />
            <h4 className="mb-0">Settings</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form>
            <Row>
              <Col md="6">
                <FloatingLabel
                  controlId="androidUserAppVersion"
                  label="Android User App Version"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    value={formik.values.androidUserAppVersion}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("androidUserAppVersion")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="iosUserAppVersion"
                  label="IOS User App Version"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    value={formik.values.iosUserAppVersion}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("iosUserAppVersion")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="kioskUserAppVersion"
                  label="Kiosk User App Version"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    value={formik.values.kioskUserAppVersion}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("kioskUserAppVersion")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="androidUserAppForceUpdate"
                  label="Android User App Force Update"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    value={formik.values.androidUserAppForceUpdate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("androidUserAppForceUpdate")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="iosUserAppForceUpdate"
                  label="IOS User App Force Update"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    value={formik.values.iosUserAppForceUpdate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("iosUserAppForceUpdate")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="kioskUserAppForceUpdate"
                  label="Android Restaurant App Version"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    value={formik.values.kioskUserAppForceUpdate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("kioskUserAppForceUpdate")}
                </FloatingLabel>
              </Col>



              <Col md="6">
                <FloatingLabel
                  controlId="maintenance"
                  label="Maintenance"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    value={formik.values.maintenance}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("maintenance")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="serviceCharge"
                  label="Service Charge"
                  className="mb-3"
                >
                  <Form.Control
                    type="number"
                    value={formik.values.serviceCharge}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("serviceCharge")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="tax"
                  label="Tax"
                  className="mb-3"
                >
                  <Form.Control
                    type="number"
                    value={formik.values.tax}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {showError("tax")}
                </FloatingLabel>
              </Col>



              <Col md="12" className="text-end">
                <Button
                  variant="primary"
                  type="submit"
                  onClick={(e) => {
                    e.preventDefault();
                    formik.handleSubmit();
                  }}
                >
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default Settings;
