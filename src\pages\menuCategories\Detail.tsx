import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Card, Button, ListGroup, Row, Col } from "react-bootstrap";
import { FaEdit } from "react-icons/fa";
import { menuService } from "services";
import { MenuCategoryInterface } from "interfaces";
import StatusComponent from "components/StatusComponent";
import ImageModal from "components/ImageModal";
import useTranslate from "hooks/useTranslate";
import BreadcrumbComponent from "components/Breadcrumb";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import DatatableComponent from "components/DatatableComponent";
import SearchInputComponent from "components/SearchInputComponent";
import AddActionComponent from "components/AddActionComponent";
import { FaList } from "react-icons/fa";
import EditActionComponent from "components/EditActionComponent";
import ViewActionComponent from "components/ViewActionComponent";
import StatusActionComponent from "components/StatusActionComponent";
import { toast } from "react-toastify";
import DeleteModal from "components/Modal/DeleteModal";
import DeleteActionComponent from "components/DeleteActionComponent";
// import { menuDetailBreadcrumbs } from "constants/breadcrums";

const MenuDetail = () => {
  const [data, setData] = useState<MenuCategoryInterface[]>([]);
  const [dataV2, setDatav2] = useState<MenuCategoryInterface[]>([]);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { translate } = useTranslate();
  const [menu, setMenu] = useState<MenuCategoryInterface | null>(null);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");
  const [totalRows, setTotalRows] = useState(0);
  const [subId, setSubId] = useState<any>("");
  const [show, setShow] = useState<any>(false);
  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);

  useEffect(() => {
    fetchMenuDetailsV2();

  }, [id]);

  useEffect(() => {
    setData([]);
    fetchMenuDetails();
  }, [page, perPage, sortBy, sortDirection, searchTerm, id]);

  const fetchMenuDetailsV2 = async () => {
    setLoading(true);
    try {
      const response = await menuService.getDetail(Number(id));
      if (response?.data?.success) {
        setMenu(response.data.data.menuCategory);

        const payload = {
          page,
          pagination: true,
          skip: (page - 1) * perPage,
          limit: perPage,
          sortBy,
          sortDirection,
          searchTerm,
          activeStatus: "ALL",
          isParent: false,
          menuCategoryId: response.data.data.menuCategory?.id
        };

        const subCategoryResponse = await menuService.getMenuCategories(payload)
        if (subCategoryResponse?.data?.success) {
          setDatav2(subCategoryResponse.data.data.docs);
          setTotalRows(subCategoryResponse.data.data.meta.totalDocs);
        }
      }
    } catch (error) {
      console.error("Error fetching menu details:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMenuDetails = async () => {
    setLoading(true);
    try {
      const response = await menuService.getDetail(Number(id));
      if (response?.data?.success) {
        setMenu(response.data.data.menuCategory);

        const payload = {
          page,
          pagination: true,
          skip: (page - 1) * perPage,
          limit: perPage,
          sortBy,
          sortDirection,
          searchTerm,
          activeStatus: "ALL",
          isParent: false,
          menuCategoryId: response.data.data.menuCategory?.id
        };

        const subCategoryResponse = await menuService.getMenuCategories(payload)
        if (subCategoryResponse?.data?.success) {
          setData(subCategoryResponse.data.data.docs);
          setTotalRows(subCategoryResponse.data.data.meta.totalDocs);
        }
      }
    } catch (error) {
      console.error("Error fetching menu details:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="text-center">{translate("common.loading")}</div>;
  }

  if (!menu) {
    return <div className="text-center">{translate("common.notFound")}</div>;
  }

  const updateStatus = async (id: number | string) => {
    try {
      await menuService.updateCategoryStatus(id);
      setData(prevData =>
        prevData.map(category =>
          category.id === id ? { ...category, isSuspended: !category.isSuspended } : category
        )
      );
    } catch (error) {
      console.error(error);
    }
  };

  const handleDelete = async (id: any) => {
    try {
      await menuService.deleteCategory(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData => prevData.filter(staff => staff.id !== id));
    } catch (error) {
      console.error(error);
    }
    handleClose(); // Close the modal after delete
  };

  //   const removeMenuCategory = (id: number | string) => {
  //     console.log({ test: id });
  //   };

  const renderCategoryStatus = (isSuspended: boolean) => {
    return <StatusComponent isSuspended={!isSuspended} />;
  };

  const removeRecord = async (sId: number | string) => {
    setSubId(sId)
    handleShow()

  };

  const renderActionColumn = (row: MenuCategoryInterface) => {
    return (
      <>
        <StatusActionComponent
          id={row.id}
          isSuspended={row.isSuspended}
          updateStatus={() => updateStatus(row.id)}
        />{" "}

        <EditActionComponent url={`/categories/${row.id}/edit`}></EditActionComponent>{" "}

        <ViewActionComponent url={`/categories/${row.id}/view`} />
        <DeleteActionComponent fn={() => removeRecord(Number(row.id))} />{" "}
      </>
    );
  };



  const columns = [
    {
      name: "Title",
      sortable: true,
      sortField: "title",
      selector: (row: MenuCategoryInterface) => row.title || "N/A",
    },
    {
      name: "Status",
      sortable: false,
      sortField: "email",
      selector: (row: MenuCategoryInterface) => renderCategoryStatus(!row.isSuspended),
    },
    {
      name: "Parent Category",
      sortable: false,
      sortField: "parentCategory",
      selector: (row: any) => row.parentCategory || "N/A",
    },
    {
      name: "Action",
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isDetail: true, entityPath: "/categories", entities: "Categories" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">{translate("menu.details")}</h4>
            {/* <Button
              variant="primary"
              onClick={() => navigate(`/categories/${id}/edit`)}
            >
              <FaEdit /> {translate("common.edit")}
            </Button> */}
          </div>
        </Card.Header>

        <Card.Body>
          <div className="flex-column align-items-start">
            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("menu.title")}: {menu.title || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("menu.type")}: {menu.type ? translate(`menu.${menu?.type?.toLowerCase()}`) : "N/A"}</p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("menu.description")}: {menu.description || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3 d-flex">
                  <p>{translate("common.status")}: <StatusComponent isSuspended={!!menu.isSuspended} /> </p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("menu.menuCategories")}: {menu.parentTitle || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p><strong>{translate("menu.image")}:</strong></p>
                  <ImageModal
                    height={"150px"}
                    thumbnailSrc={menu.image}
                    altText={menu.title}
                  />
                </div>
              </Col>
            </Row>

          </div>
        </Card.Body>


        {
          dataV2?.length > 0 &&
          <Card className="custom-card">
            <Card.Header>
              <div className="d-flex justify-content-between align-items-center">
                <div className="d-flex gap-2 align-items-center custom-card-header-title">
                  <FaList />
                  <h4 className="mb-0">Sub-Categories</h4>
                </div>
              </div>
            </Card.Header>

            <Card.Body>
              <div className="d-flex gap-3 pb-4 search-box">
                <SearchInputComponent
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                />
              </div>

              {
                data?.length > 0 &&
                <DatatableComponent
                  columns={columns}
                  data={data}
                  loading={loading}
                  totalRows={totalRows}
                  setPage={setPage}
                  setPerPage={setPerPage}
                  setSortBy={setSortBy}
                  setSortDirection={setSortDirection}
                />
              }
              <DeleteModal title="Sub Category" id={subId} show={show} handleDelete={handleDelete} handleClose={handleClose} />
            </Card.Body>
          </Card>}


      </Card>
      {/* </div> */}
    </>
  );
};

export default MenuDetail;
