import React, { useEffect, useState } from "react";
import {
    Card,
    Col,
    Form,
    Row,
    Button,
    FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import ImageUploadComponent from "components/ImageUploadComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { useNavigate, useParams } from "react-router-dom";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import PhoneInput from "react-phone-input-2";
import { branchService } from "services/branch.service";

const createValidationSchema = (isNewStaff: boolean) =>
    yup.object().shape({
        name: yup
            .string()
            .trim()
            .required("Name is required")
            .min(3, "Name must be at least 3 characters")
            .max(50, "Name cannot exceed 50 characters"),
        address: yup
            .string()
            .trim()
            .required("address is required")
            .min(3, "address must be at least 3 characters")
            .max(200, "address cannot exceed 200 characters"),
        city: yup
            .string()
            .trim()
            .required("city is required")
            .min(3, "city must be at least 3 characters")
            .max(50, "city cannot exceed 50 characters"),
        email: yup.string().email("Invalid email").required("Email is required"),
        password: isNewStaff ? yup
            .string()
            .required("Please enter a password")
            .matches(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])(?=.{8,})/,
                "Must contain 8 characters, one uppercase, one lowercase, one number, and one special case character"
            )
            : yup.string(),
        confirmPassword: isNewStaff ? yup
            .string()
            .required("Enter confirm password")
            .oneOf(
                [yup.ref("password")],
                "New and confirm passwords must match"
            ) : yup.string(),
        countryCode: yup.string().required("Country code is required"),
        phone: yup.string().trim().required("Phone number is required"),
        avatar: yup.string().required("Avatar is required"),
    });

const AddStaff: React.FC = () => {
    const { translate } = useTranslate();
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();

    const [showPassword, setShowPassword] = useState(false);

    const [initialValues, setInitialValues] = useState({
        name: "",
        email: "",
        countryCode: "+91",
        phone: "",
        avatar: "",
        password: "",
        confirmPassword: "",
        city: "",
        address: ""
    });
    const isNewStaff = !id;

    const fetchBranch = async (id: string) => {
        try {
            const {
                data: { data: { branch } },
            } = await branchService.getDetail(id);
            setInitialValues({ name: branch.restaurantName, ...branch });
        } catch (error) {
            toast.error("Failed to fetch staff data");
        }
    };

    useEffect(() => {
        if (id) {
            fetchBranch(id);
        }
    }, [id]);

    const formik: any = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: createValidationSchema(isNewStaff),
        onSubmit: async (values: any) => {
            try {
                if (id) {
                    if ("geolocation" in navigator) {
                        navigator.geolocation.getCurrentPosition(
                            (position) => {
                                (async () => {
                                    const { latitude, longitude } = position.coords;
                                    const { confirmPassword, ...restValues } = values

                                    await branchService.update(id, { ...restValues, longitude: longitude, latitude: latitude });
                                    toast.success("Staff updated successfully");
                                })()
                            });
                    }

                } else if ("geolocation" in navigator) {
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            (async () => {
                                const { latitude, longitude } = position.coords;

                                const { confirmPassword, ...restValues } = values
                                await branchService.add({ ...restValues, longitude: longitude, latitude: latitude });
                                toast.success("Branch added successfully");
                                navigate("/branches");
                            })()
                        });
                }

            } catch (err) {
                toast.error("Failed to submit form");
            } finally {
            }
        },
    });

    const { values, handleChange } = formik

    return (
        <>
            <BreadcrumbComponent breadcrumbs={getBreadcrumbs(id ? { isEdit: true, entityPath: "/Branches", entities: "Branch" } : { isAdd: true, entityPath: "/branches", entities: "Branches" })} />
            <Card className="custom-card">
                <Card.Header>
                    <h4 className="mb-0">{id ? translate("branch.update") : translate("branch.add")}</h4>
                </Card.Header>
                <Card.Body>
                    <Form onSubmit={formik.handleSubmit}>
                        <Row>
                            <Col md="6">
                                <FloatingLabel
                                    controlId="name"
                                    label={translate("staff.name")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type="text"
                                        name="name"
                                        value={formik.values.name}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={formik.touched.name && !!formik.errors.name}
                                        autoComplete="off"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.name && formik.errors.name}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>

                            <Col md="6">
                                <FloatingLabel
                                    controlId="email"
                                    label={translate("staff.email")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type="email"
                                        name="email"
                                        value={formik.values.email}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={formik.touched.email && !!formik.errors.email}
                                        autoComplete="off"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.email && formik.errors.email}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>



                            {isNewStaff && <Col md="6">
                                <FloatingLabel
                                    controlId="password"
                                    label={translate("staff.password")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type={showPassword ? "text" : "password"}
                                        name="password"
                                        value={formik.values.password}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={
                                            formik.touched.password && !!formik.errors.password
                                        }
                                        autoComplete="off"
                                    />
                                    <Button
                                        className="eye-icon"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                                    </Button>
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.password && formik.errors.password}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>
                            }

                            {isNewStaff && <Col md="6">
                                <FloatingLabel
                                    controlId="confirmPassword"
                                    label={translate("branch.confirmPassword")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type={showPassword ? "text" : "password"}
                                        name="confirmPassword"
                                        value={formik.values.confirmPassword}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={
                                            formik.touched.confirmPassword && !!formik.errors.confirmPassword
                                        }
                                        autoComplete="off"
                                    />
                                    <Button
                                        className="eye-icon"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                                    </Button>
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.confirmPassword && formik.errors.confirmPassword}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>
                            }

                            <Col md="6">
                                <FloatingLabel
                                    controlId="address"
                                    label={translate("branch.address")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type="text"
                                        name="address"
                                        value={formik.values.address}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={formik.touched.address && !!formik.errors.address}
                                        autoComplete="off"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.address && formik.errors.address}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>

                            <Col md="6">
                                <FloatingLabel
                                    controlId="city"
                                    label={translate("branch.city")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type="text"
                                        name="city"
                                        value={formik.values.city}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={formik.touched.city && !!formik.errors.city}
                                        autoComplete="off"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.city && formik.errors.city}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>
                            <Col md="6">
                                {/* <FloatingLabel
                                    controlId="phone"
                                    label={translate("staff.phone")}
                                    className="mb-3"
                                > */}
                                <PhoneInput
                                    value={`${values.countryCode}${values.phone}`}
                                    country={"in"}
                                    onChange={(phone: string, countryData: { dialCode: string }) => {
                                        const phoneWithoutDialCode = phone?.slice(countryData?.dialCode?.length);
                                        handleChange({ target: { name: 'phone', value: phoneWithoutDialCode } });
                                        handleChange({ target: { name: 'countryCode', value: countryData?.dialCode } });
                                    }}
                                    placeholder="Mobile Number"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {formik.touched.phone && formik.errors.phone}
                                </Form.Control.Feedback>
                                {/* </FloatingLabel> */}
                            </Col>

                            <Col md="12" className="mt-4">
                                <Form.Group className="mb-3">
                                    <Form.Label>{translate("staff.upload-image")}</Form.Label>
                                    <ImageUploadComponent
                                        bucketType="staffs"
                                        handleOnChange={(e: any) => formik.handleChange(e)}
                                        image={formik.values.avatar}
                                        setImage={(field, value) =>
                                            formik.setFieldValue(field, value)
                                        }
                                        handleRemove={() => formik.setFieldValue("avatar", "")}
                                        fieldName="avatar"
                                    />
                                    {formik.touched.avatar && formik.errors.avatar && (
                                        <small className="text-danger">
                                            {formik.errors.avatar}
                                        </small>
                                    )}
                                </Form.Group>
                            </Col>

                            <Col md="12" className="text-end">
                                <Button variant="primary" type="submit">
                                    Submit
                                </Button>
                            </Col>
                        </Row>
                    </Form>
                </Card.Body>
            </Card>
        </>
    );
};

export default AddStaff;
