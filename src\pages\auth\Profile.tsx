import BreadcrumbComponent from "components/Breadcrumb";
import ImageUploadComponent from "components/ImageUploadComponent";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
// import { profileBreadcrumbs } from "constants/breadcrums";
import { useFormik } from "formik";
import useCurrentUser from "hooks/useCurrentUser";
import { UserProfile } from "interfaces/ProfileInterface";
import { ReactNode, useEffect, useState } from "react";
import { Button, Card, Col, FloatingLabel, Form, Row } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { myAccountService } from "services";
import * as yup from "yup";

const Profile = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState<UserProfile>();

  const { setAvatar } = useCurrentUser();



  const formik = useFormik({
    initialValues: {
      avatar: user?.avatar || '',
      restaurantName: user?.restaurantName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || '',
      city: user?.city || '',
      latitude: user?.latitude || 0,
      longitude: user?.longitude || 0,
      // restaurantDoc: user?.restaurantDoc || '',
      countrySortName: "en",
      countryCode: "+91",
    },
    validationSchema: yup.object().shape({
      restaurantName: yup.string().trim().required("Restaurant Name is required"),
      email: yup.string().email("Invalid email").required("Email is required"),
      // countryCode: yup.string().required("Country Code is required"),
      phone: yup.string().trim().required("Phone number is required"),
      address: yup.string().trim().required("Address is required"),
      city: yup.string().trim().required("City is required"),
      latitude: yup.number().required("Latitude is required"),
      longitude: yup.number().required("Longitude is required"),
    }),
    enableReinitialize: true,
    onSubmit: async (values: UserProfile) => {
      try {
        // Simulate API call to update profile
        // Replace with actual API call using myAccountService.updateProfile(values)
        const response = await myAccountService.updateProfile(values);
        // Example toast notifications based on response
        toast.success("Profile updated successfully");
        setAvatar(values?.avatar);
        // navigate("/dashboard");
      } catch (error) {
        console.error("Error updating profile:", error);
        toast.error("Failed to update profile. Please try again.");
      }
    },
  });

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const res = await myAccountService.getProfile();
        const fetchedUser = res.data.data.restaurant;
        setUser(fetchedUser);
      } catch (error) {
        console.error("Error fetching profile:", error);
        toast.error("Failed to fetch profile. Please try again.");
      }
    };

    fetchProfile();
  }, []);

  const showError = <T extends keyof UserProfile>(field: T) =>
    formik.touched[field] && formik.errors[field] ? (
      <span className="text-danger">{formik.errors[field] as ReactNode}</span>
    ) : null;

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isSingle: true, entityPath: "/profile", entities: "Profile" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="custom-card-header-title">
            <h4 className="mb-0">Profile</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <FloatingLabel
                  controlId="restaurantName"
                  label="Restaurant Name"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    {...formik.getFieldProps("restaurantName")}
                  />
                  {showError("restaurantName")}
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="email" label="Email" className="mb-3">
                  <Form.Control
                    type="email"
                    readOnly={true}
                    {...formik.getFieldProps("email")}
                  />
                  {showError("email")}
                </FloatingLabel>
              </Col>

              {/* <Col md="6">
                <FloatingLabel
                  controlId="countryCode"
                  label="Country Code"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    {...formik.getFieldProps("countryCode")}
                  />
                  {showError("countryCode")}
                </FloatingLabel>
              </Col> */}
              <Col md="6">
                <FloatingLabel controlId="phone" label="Phone" className="mb-3">
                  <Form.Control
                    type="text"
                    readOnly={true}
                    inputMode="numeric"
                    pattern="[0-9]*"
                    {...formik.getFieldProps("phone")}
                    onChange={(e) => {
                      // Allow only digits by filtering out non-numeric characters
                      const value = e.target.value.replace(/\D/g, '');
                      formik.setFieldValue("phone", value);
                    }}
                  />
                  {showError("phone")}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="address"
                  label="Address"
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    {...formik.getFieldProps("address")}
                  />
                  {showError("address")}
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel controlId="city" label="City" className="mb-3">
                  <Form.Control type="text" {...formik.getFieldProps("city")} />
                  {showError("city")}
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel
                  controlId="latitude"
                  label="Latitude"
                  className="mb-3"
                >
                  <Form.Control
                    type="number"
                    step="any"
                    readOnly={true}
                    {...formik.getFieldProps("latitude")}
                  />
                  {showError("latitude")}
                </FloatingLabel>
              </Col>
              <Col md="6">
                <FloatingLabel
                  controlId="longitude"
                  label="Longitude"
                  className="mb-3"
                >
                  <Form.Control
                    type="number"
                    step="any"
                    readOnly={true}
                    {...formik.getFieldProps("longitude")}
                  />
                  {showError("longitude")}
                </FloatingLabel>
              </Col>

              <Row>
                <Col md="6">
                  <Form.Group className="mb-3">
                    <Form.Label>Avatar</Form.Label>
                    <ImageUploadComponent
                      bucketType="restaurants"
                      handleOnChange={(e: any) => formik.handleChange(e)}
                      image={formik.values.avatar}
                      setImage={(field, value) =>
                        formik.setFieldValue(field, value)
                      }
                      handleRemove={() => formik.setFieldValue("avatar", "")}
                      fieldName="avatar"
                    />
                    {formik.touched.avatar && formik.errors.avatar && (
                      <small className="text-danger">{formik.errors.avatar}</small>
                    )}
                  </Form.Group>
                </Col>
              </Row>

              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default Profile;
