import AddActionComponent from "components/AddActionComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import SearchInputComponent from "components/SearchInputComponent";
import StatusActionComponent from "components/StatusActionComponent";
import StatusComponent from "components/StatusComponent";
import ViewActionComponent from "components/ViewActionComponent";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { StaffInterface } from "interfaces";
// import { staffsListBreadcrumbs } from "constants/breadcrums";
import { staffService } from "services";
import DeleteActionComponent from "components/DeleteActionComponent";
import { toast } from "react-toastify";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

const Staffs = () => {
  const [data, setData] = useState<StaffInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");

  const fetchData = async () => {
    setLoading(true);
    const payload = {
      page,
      pagination: true,
      skip: (page - 1) * perPage,
      limit: perPage,
      sortBy,
      sortDirection,
      searchTerm,
      activeStatus: "ALL",
    };
    try {
      const response = await staffService.getAll(payload);
      setData(response.data.data.docs);
      setTotalRows(response.data.data.meta.totalDocs);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (id: number | string) => {
    try {
      await staffService.updateStatus(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData =>
        prevData.map(staff =>
          staff.id === id ? { ...staff, isSuspended: !staff.isSuspended } : staff
        )
      );
    } catch (error) {
      console.error(error);
    }
  };

  const removeRecord = async (id: number | string) => {
    try {
      await staffService.remove(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData => prevData.filter(staff => staff.id !== id));
    } catch (error) {
      console.error(error);
    }
  };

  const renderStaffStatus = (isSuspended: boolean) => {
    return <StatusComponent isSuspended={!isSuspended} />;
  };

  const renderActionColumn = (row: StaffInterface) => {
    return (
      <>
        <StatusActionComponent
          id={row.id}
          isSuspended={row.isSuspended}
          updateStatus={() => updateStatus(row.id)}
        />{" "}

        <EditActionComponent url={`/staffs/${row.id}/edit`}></EditActionComponent>{" "}

        <ViewActionComponent url={`/staffs/${row.id}/view`} />{" "}

        <DeleteActionComponent fn={() => removeRecord(row.id)} />{" "}
      </>
    );
  };

  const columns = [
    {
      name: "Name",
      sortable: true,
      sortField: "name",
      selector: (row: StaffInterface) => row.name || "N/A",
    },
    {
      name: "Email",
      sortable: false,
      sortField: "email",
      selector: (row: StaffInterface) => row.email || "N/A",
    },
    {
      name: "Status",
      sortable: false,
      sortField: "status",
      selector: (row: StaffInterface) => renderStaffStatus(!row.isSuspended),
    },
    {
      name: "Action",
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [page, perPage, sortBy, sortDirection, searchTerm]);

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/staff", entities: "Staffs Listing" })} />
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center custom-card-header-title">
              <FaList />
              <h4 className="mb-0">Staffs</h4>
            </div>
            <div>
              <AddActionComponent url="/staffs/add" />
            </div>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex gap-3 pb-4 search-box">
            <SearchInputComponent
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          <DatatableComponent
            columns={columns}
            data={data}
            loading={loading}
            totalRows={totalRows}
            setPage={setPage}
            setPerPage={setPerPage}
            setSortBy={setSortBy}
            setSortDirection={setSortDirection}
          />
        </Card.Body>
      </Card>
    </>
  );
};

export default Staffs;
