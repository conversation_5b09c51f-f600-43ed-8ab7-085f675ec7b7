import AddActionComponent from "components/AddActionComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import SearchInputComponent from "components/SearchInputComponent";
import StatusActionComponent from "components/StatusActionComponent";
import StatusComponent from "components/StatusComponent";
import ViewActionComponent from "components/ViewActionComponent";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { CustomerInterface } from "interfaces";
// import { customersListBreadcrumbs } from "constants/breadcrums";
import { customerService } from "services";
import DeleteActionComponent from "components/DeleteActionComponent";
import { toast } from "react-toastify";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

const Customers = () => {
  const [data, setData] = useState<CustomerInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");

  const fetchData = async () => {
    setLoading(true);
    const payload = {
      page,
      pagination: true,
      skip: (page - 1) * perPage,
      limit: perPage,
      sortBy,
      sortDirection,
      searchTerm,
      activeStatus: "ALL",
    };
    try {
      const response = await customerService.getAll(payload);
      setData(response.data.data.docs);
      setTotalRows(response.data.data.meta.totalDocs);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  function toggleSuspension(customer: any, id: any): any {
    if (customer.id === id) {
      return { ...customer, isSuspended: !customer.isSuspended };
    }
    return customer;
  }

  const updateStatus = async (id: number | string) => {
    try {
      await customerService.updateStatus(id).then((response) => {
        if (response.status === 200) {

          const updatedData: any = data.map(customer => toggleSuspension(customer, id));
          setData(updatedData)
          toast.success(response?.data?.message);
        }
      }).catch((error) => {
        console.log(error)
        toast.error(error?.data?.message)
      });;
    } catch (error) {
      console.error(error);
    }
  };

  const removeRecord = async (id: number | string) => {
    try {
      await customerService.remove(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        console.log(error)
        toast.error(error?.data?.message)
      });
      setData(prevData => prevData.filter(customer => customer.id !== id));
    } catch (error) {
      console.error(error);
    }
  };

  const renderCustomerStatus = (isSuspended: boolean) => {
    return <StatusComponent isSuspended={!isSuspended} />;
  };

  const renderActionColumn = (row: CustomerInterface) => {
    return (
      <>
        <StatusActionComponent
          id={row.id}
          isSuspended={row.isSuspended}
          updateStatus={() => updateStatus(row.id)}
        />
        {" "}

        {/* <EditActionComponent url={`/customers/${row.id}/edit`}></EditActionComponent>{" "} */}

        <ViewActionComponent url={`/customers/${row.id}/view`} />{" "}

        {/* <DeleteActionComponent fn={() => removeRecord(row.id)} />{" "} */}
      </>
    );
  };

  const columns = [
    {
      name: "Name",
      sortable: true,
      sortField: "name",
      selector: (row: CustomerInterface) => `${row.firstName} ${row.lastName}` || "N/A",
    },
    {
      name: "Email",
      sortable: false,
      sortField: "email",
      selector: (row: CustomerInterface) => row.email || "N/A",
    },
    {
      name: "Status",
      sortable: false,
      sortField: "status",
      selector: (row: CustomerInterface) => renderCustomerStatus(!row.isSuspended),
    },
    {
      name: "Action",
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [page, perPage, sortBy, sortDirection, searchTerm]);

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/customers", entities: "Customers" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center custom-card-header-title">
              <FaList />
              <h4 className="mb-0">Customers List</h4>
            </div>
            {/* <div>
              <AddActionComponent url="/customers/add" />
            </div> */}
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex gap-3 pb-4 search-box">
            <SearchInputComponent
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          <DatatableComponent
            columns={columns}
            data={data}
            loading={loading}
            totalRows={totalRows}
            setPage={setPage}
            setPerPage={setPerPage}
            setSortBy={setSortBy}
            setSortDirection={setSortDirection}
          />
        </Card.Body>
      </Card>
    </>
  );
};

export default Customers;
