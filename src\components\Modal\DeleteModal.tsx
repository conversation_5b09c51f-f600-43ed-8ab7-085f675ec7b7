import React from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

const DeleteModal: any = ({ show, handleClose, handleDelete, id, title }: any) => {


    return (
        <Modal show={show} onHide={handleClose} centered>
            <Modal.Header closeButton>
                <Modal.Title>Confirm Deletion</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                Are you sure you want to delete this {title}? This action cannot be undone.
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={handleClose}>
                    Cancel
                </Button>
                <Button variant="danger" onClick={() => handleDelete(id)}>
                    Delete
                </Button>
            </Modal.Footer>
        </Modal>

    );
};

export default DeleteModal;
