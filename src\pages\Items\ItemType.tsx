import React from "react";
import { Form } from "react-bootstrap";
import ImageUploadComponent from "components/ImageUploadComponent"; // Update this import if needed
import { ItemTypeInterface } from "interfaces";

interface ItemTypeRowProps {
  index: number;
  item: ItemTypeInterface;
  changeHandler: (
    index: number,
    value: string | number,
    fieldName: "title" | "image" | "price"
  ) => void;
  onRemove: (index: number) => void;
  errors: any;
}

const ItemTypeRow = ({
  index,
  item,
  onRemove,
  changeHandler,
  errors,
}: ItemTypeRowProps) => {
  return (
    <div className="item-type-row p-4">
      <Form.Group className="mb-3">
        <Form.Label>Item Name</Form.Label>
        <Form.Control
          type="text"
          value={item.title}
          onChange={(e) => changeHandler(index, e.target.value, "title")}
        />
        {errors?.title ? (
          <small className="text-danger">{errors?.title}</small>
        ) : null}
      </Form.Group>
      <Form.Group className="mb-3">
        <Form.Label>Item Image</Form.Label>
        <ImageUploadComponent
          bucketType="items"
          handleOnChange={(e) => changeHandler(index, e.target.value, "image")}
          image={item.image}
          setImage={(field, value) => changeHandler(index, value, "image")}
          handleRemove={() => changeHandler(index, "", "image")}
          fieldName="image"
        />
        {errors?.image ? (
          <small className="text-danger">{errors?.image}</small>
        ) : null}
      </Form.Group>
      <Form.Group className="mb-3">
        <Form.Label>Item Price</Form.Label>
        <Form.Control
          type="number"
          inputMode="numeric"
          pattern="[0-9]*"
          value={item.price}
          onChange={(e) => {
            // Allow only digits by filtering out non-numeric characters
            const value = e.target.value.replace(/\D/g, '');
            changeHandler(index, parseFloat(value), "price")
          }}
        // onChange={(e) =>
        //   changeHandler(index, parseFloat(e.target.value), "price")
        // }
        />
        {errors?.price ? (
          <small className="text-danger">{errors?.price}</small>
        ) : null}
      </Form.Group>
      <button
        type="button"
        onClick={() => onRemove(index)}
        className="btn btn-danger"
      >
        Remove
      </button>
    </div>
  );
};

export default ItemTypeRow;
