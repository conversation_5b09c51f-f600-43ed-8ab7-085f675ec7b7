import { useState, useCallback } from 'react';
import { ReturnType } from './bool.interface';

export function useBoolean(defaultValue?: boolean): ReturnType {

    const [value, setValue] = useState(!!defaultValue);

    const onTrue = useCallback(() => { setValue(true); }, []);

    const onFalse = useCallback(() => { setValue(false); }, []);

    const onToggle = useCallback(() => { setValue((prev) => !prev); }, []);

    return { value, onTrue, onFalse, onToggle, setValue, };
}
