import { CustomizationInterface } from "interfaces";
import base from "./base.service";

const list = (payload: any) => base.get(`/customization`, { params: payload });

const deleteModifier = (id: string | number) => base.delete(`/customization/${id}`)
const add = (payload: CustomizationInterface) => base.post(`/customization`, { ...payload });
const getDetails = (id: number | string) =>
  base.get(`/customization/${id}`);
const update = (id: string | number, payload: any) =>
  base.put(`/customization/${id}`, { ...payload });

// const add = (payload: AddBranch) =>
//   base.post(
//     `/restaurant-branch`,{...payload}
//   );

export const customizationService = {
  list,
  deleteModifier,
  add,
  getDetails,
  update
};