export const formattedDate = (date: any) => {
  if (!date) {
    date = new Date();
  }
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};

export const getFormattedDate = (value: Date) => {
  if (!value) {
    return "";
  }

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sept",
    "Oct",
    "Nov",
    "Dec",
  ];

  const formattedDate = new Date(value);
  const date = formattedDate.getDate();
  const month = formattedDate.getMonth();
  const year = formattedDate.getFullYear();

  return `${months[month]} ${date}, ${year}`;
};


export const getFormattedDateMMDDYYYY = (value: Date) => {
  if (!value) {
    return "";
  }

  const formattedDate = new Date(value);
  const month = String(formattedDate.getMonth() + 1).padStart(2, "0"); // months are 0-based
  const date = String(formattedDate.getDate()).padStart(2, "0");
  const year = String(formattedDate.getFullYear()); // get last 2 digits

  return `${year}-${month}-${date}`;
};
