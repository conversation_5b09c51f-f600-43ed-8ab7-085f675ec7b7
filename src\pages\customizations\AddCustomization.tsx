import React, { useEffect, useState } from "react";
import {
    Card,
    Col,
    Form,
    Row,
    Button,
    FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { customizationService } from "services";
import { CustomizationInterface } from "interfaces";
import { useNavigate, useParams } from "react-router-dom";
// import { customersAddBreadcrumbs, customersUpdateBreadcrumbs } from "constants/breadcrums";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

// Helper function to create validation schema
const createValidationSchema = (isNewCustomer: boolean) =>
    yup.object().shape({
        title: yup
            .string()
            .trim()
            .required("Title is required")
            .min(3, "Title  must be at least 3 characters")
            .max(50, "Title  cannot exceed 50 characters"),
        isLayering: yup.string().required("isLayering is required"),
    });

// Main component
const AddCustomizaion: React.FC = () => {
    const { translate } = useTranslate();
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();


    const [initialValues, setInitialValues] = useState({
        title: "", isLayering: false,
    });

    const isNewCustomer = !id;


    useEffect(() => {
        if (id) {
            const fetchStaff = async () => {
                try {
                    const data: any = await customizationService.getDetails(id);
                    if (data?.data?.success) {
                        setInitialValues({ title: data?.data?.data?.item?.title, isLayering: data?.data?.data?.item?.isLayering });
                    }
                } catch (error) {
                    console.error("Error fetching staff:", error);
                    toast.error("Failed to fetch staff data");
                }
            };

            fetchStaff();
        }
    }, [id]);

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: createValidationSchema(isNewCustomer),
        onSubmit: async (values) => {
            try {
                if (id) {
                    await customizationService.update(id, values as any);
                    toast.success("Staff updated successfully");
                } else {
                    await customizationService.add(values as any);
                    toast.success("Staff added successfully");
                }
                navigate("/customizations");
            } catch (err) {
                console.error("Error submitting form:", err);
                toast.error("Failed to submit form");
            }
        },
    });

    return (
        <>
            <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isAdd: true, entityPath: "/Customizations", entities: "Modifiers" })} />

            <Card className="custom-card">
                <Card.Header>
                    <h4 className="mb-0">{id ? translate("staff.edit") : translate("modifier.add")}</h4>
                </Card.Header>
                <Card.Body>
                    <Form onSubmit={formik.handleSubmit}>
                        <Row>
                            <Col md="6">
                                <FloatingLabel
                                    controlId="title"
                                    label={translate("staff.name")}
                                    className="mb-3"
                                >
                                    <Form.Control
                                        type="text"
                                        name="title"
                                        value={formik.values.title}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        isInvalid={formik.touched.title && !!formik.errors.title}
                                        autoComplete="off"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {formik.touched.title && formik.errors.title}
                                    </Form.Control.Feedback>
                                </FloatingLabel>
                            </Col>


                            <Col>
                                <Form.Check
                                    type="switch"
                                    id="layering-switch"
                                    label="is layering allowed?"
                                    checked={formik.values.isLayering}

                                    onChange={(event) => {
                                        formik.setFieldValue('isLayering', event?.target?.checked)
                                    }}
                                />
                            </Col>


                            <Col md="12" className="text-end">
                                <Button variant="primary" type="submit">
                                    Submit
                                </Button>
                            </Col>
                        </Row>
                    </Form>
                </Card.Body>
            </Card>
        </>
    );
};

export default AddCustomizaion;
