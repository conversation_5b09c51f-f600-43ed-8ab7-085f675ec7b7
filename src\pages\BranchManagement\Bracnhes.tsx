import AddActionComponent from "components/AddActionComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import EditActionComponent from "components/EditActionComponent";
import SearchInputComponent from "components/SearchInputComponent";
import StatusActionComponent from "components/StatusActionComponent";
import StatusComponent from "components/StatusComponent";
import ViewActionComponent from "components/ViewActionComponent";
import { useEffect, useRef, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import { StaffInterface } from "interfaces";
// import { staffsListBreadcrumbs } from "constants/breadcrums";
import DeleteActionComponent from "components/DeleteActionComponent";
import { toast } from "react-toastify";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { branchService } from "services/branch.service";
import DeleteModal from "components/Modal/DeleteModal";

const Branches = () => {
    const [data, setData] = useState<StaffInterface[]>([]);
    const [loading, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [page, setPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [sortBy, setSortBy] = useState("id");
    const [sortDirection, setSortDirection] = useState("ASC");
    const [searchTerm, setSearchTerm] = useState("");

    const [show, setShow] = useState<any>(false);
    const [id, setId] = useState<any>("");

    const handleShow = () => setShow(true);
    const handleClose = () => setShow(false);

    const [csvData, setCsvData] = useState<string[][] | null>(null);
    const inputFileRef = useRef<HTMLInputElement | null>(null);

    const handleDelete = async (branchId: any) => {
        // Perform delete action here
        try {
            await branchService.remove(branchId).then((response) => {
                toast.success(response?.data?.message);
            }).catch((error) => {
                toast.error(error?.data?.message);
            });
            setData(prevData => prevData.filter(branch => branch.id !== id));
        } catch (error) {
            console.error(error);
        }

        handleClose(); // Close the modal after delete
    };

    const fetchData = async () => {
        setLoading(true);
        const payload = {
            page,
            pagination: true,
            skip: (page - 1) * perPage,
            limit: perPage,
            sortBy,
            sortDirection,
            searchTerm,
            activeStatus: "ALL",
        };
        try {
            const response = await branchService.getAll(payload);
            setData(response.data.data.docs);
            setTotalRows(response.data.data.meta.totalDocs);
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const updateStatus = async (id: number | string) => {
        try {
            await branchService.updateStatus(id).then((response) => {
                toast.success(response?.data?.message);
            }).catch((error) => {
                toast.error(error?.data?.message);
            });
            setData(prevData =>
                prevData.map(branch =>
                    branch.id === id ? { ...branch, isSuspended: !branch.isSuspended } : branch
                )
            );
        } catch (error) {
            console.error(error);
        }
    };

    const removeRecord = async (id: number | string) => {
        setId(id)
        handleShow()

    };

    const renderStaffStatus = (isSuspended: boolean) => {
        return <StatusComponent isSuspended={!isSuspended} />;
    };

    const renderActionColumn = (row: StaffInterface) => {
        return (
            <>
                <StatusActionComponent
                    id={row.id}
                    isSuspended={row.isSuspended}
                    updateStatus={() => updateStatus(row.id)}
                />{" "}

                <EditActionComponent url={`/branches/edit/${row.id}`}></EditActionComponent>{" "}

                {/* <ViewActionComponent url={`/staffs/${row.id}/view`} />{" "} */}

                <DeleteActionComponent fn={() => removeRecord(row.id)} />{" "}
            </>
        );
    };

    const columns = [
        {
            name: "Name",
            sortable: true,
            sortField: "name",
            selector: (row: StaffInterface) => row.name || "N/A",
        },
        {
            name: "Email",
            sortable: false,
            sortField: "email",
            selector: (row: StaffInterface) => row.email || "N/A",
        },
        //  {
        //     name: "Phone",
        //     sortable: false,
        //     sortField: "phone",
        //     selector: (row: StaffInterface) => row.phone || "N/A",
        // },
        {
            name: "Status",
            sortable: false,
            sortField: "status",
            selector: (row: StaffInterface) => renderStaffStatus(!row.isSuspended),
        },
        {
            name: "Action",
            selector: (row: any) => renderActionColumn(row),
        },
    ];

    useEffect(() => {
        fetchData();
    }, [page, perPage, sortBy, sortDirection, searchTerm]);

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            try {
                // Step 1: Read and parse the file locally (optional)
                const reader = new FileReader();
                reader.onload = (e) => {
                    const text = e.target?.result as string;
                    const rows = text.split("\n").map((row) => row.split(","));
                    setCsvData(rows);
                };
                reader.readAsText(file);

                // Step 2: Prepare file for upload
                const formData: any = new FormData();
                formData.append("file", file);

                // Step 3: Upload to API
                const response: any = await branchService.upload(formData)

                if (response?.data?.success) {
                    fetchData()
                }

                if (inputFileRef.current) {
                    inputFileRef.current.value = "";
                }

            } catch (error) {
                console.error("Upload Error:", error);
            }
        }
    };

    const uploadCSV = () => {
        if (inputFileRef.current) {
            inputFileRef.current.click(); // Trigger the hidden input
        }
    }

    return (
        <>
            <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/Branches", entities: "Branch Listing" })} />
            <Card className="custom-card">
                <Card.Header>
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex gap-2 align-items-center custom-card-header-title">
                            <FaList />
                            <h4 className="mb-0">Branches</h4>
                        </div>
                        <div style={{ justifyContent: 'space-between', display: 'flex', width: '180px' }}>
                            <button className="upload-btn" onClick={uploadCSV}> Upload CSV </button>
                            <input
                                ref={inputFileRef}
                                type="file"
                                accept=".csv"
                                onChange={handleFileChange}
                                style={{ marginBottom: "1rem", display: 'none' }}
                            />
                            <AddActionComponent url="/branches/add" />
                        </div>
                    </div>
                </Card.Header>

                <Card.Body>
                    <div className="d-flex gap-3 pb-4 search-box">
                        <SearchInputComponent
                            searchTerm={searchTerm}
                            setSearchTerm={setSearchTerm}
                        />
                    </div>

                    <DatatableComponent
                        columns={columns}
                        data={data}
                        loading={loading}
                        totalRows={totalRows}
                        setPage={setPage}
                        setPerPage={setPerPage}
                        setSortBy={setSortBy}
                        setSortDirection={setSortDirection}
                    />

                    <DeleteModal title="Branch" id={id} show={show} handleDelete={handleDelete} handleClose={handleClose} />
                </Card.Body>
            </Card>
        </>
    );
};

export default Branches;
