import base from "./base.service";

const getBucketUrl = (payload) =>
  base.get(`/utils/s3-upload-urls`, { params: payload });

const uploadFile = (payload) => fetch(payload?.url, payload?.requestOptions);

const contactUs = (payload) => base.post("/utils/contact-us", payload);

const adminSettings = () => base.get("/admin-settings");

export const utilService = {
  getBucketUrl,
  uploadFile,
  contactUs,
  adminSettings,
};
