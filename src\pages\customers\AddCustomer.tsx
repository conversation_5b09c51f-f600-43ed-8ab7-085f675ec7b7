import React, { useEffect, useState } from "react";
import {
  Card,
  Col,
  Form,
  Row,
  Button,
  FloatingLabel,
} from "react-bootstrap";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import ImageUploadComponent from "components/ImageUploadComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import useTranslate from "hooks/useTranslate";
import { customerService } from "services";
import { CustomerInterface } from "interfaces";
import { useNavigate, useParams } from "react-router-dom";
// import { customersAddBreadcrumbs, customersUpdateBreadcrumbs } from "constants/breadcrums";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { getBreadcrumbs } from "components/Breadcrumb/validator";

// Helper function to create validation schema
const createValidationSchema = (isNewCustomer: boolean) =>
  yup.object().shape({
    firstName: yup
      .string()
      .trim()
      .required("First Name is required")
      .min(3, "First Name  must be at least 3 characters")
      .max(50, "First Name  cannot exceed 50 characters"),

    lastName: yup
      .string()
      .trim()
      .required("Last Name is required")
      .min(3, "Last Name must be at least 3 characters")
      .max(50, "Last Name cannot exceed 50 characters"),

    email: yup.string().email("Invalid email").required("Email is required"),

    password: isNewCustomer ? yup
      .string()
      .required("Please enter a password")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])(?=.{8,})/,
        "Must contain 8 characters, one uppercase, one lowercase, one number, and one special case character"
      )
      : yup.string(),

    countryCode: yup.string().required("Country code is required"),

    phone: yup.string().trim().required("Phone number is required"),

    avatar: yup.string().required("Avatar is required"),
  });

// Main component
const AddCustomer: React.FC = () => {
  const { translate } = useTranslate();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const [showPassword, setShowPassword] = useState(false);

  const [initialValues, setInitialValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    countryCode: "+91",
    phone: "",
    avatar: "",
    password: "",
  });

  const isNewCustomer = !id;

  useEffect(() => {
    if (id) {
      const fetchStaff = async () => {
        try {
          const {
            data: { data: { staff } },
          } = await customerService.getDetail(id);
          setInitialValues(staff as CustomerInterface);
        } catch (error) {
          console.error("Error fetching staff:", error);
          toast.error("Failed to fetch staff data");
        }
      };

      fetchStaff();
    }
  }, [id]);

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: createValidationSchema(isNewCustomer),
    onSubmit: async (values) => {
      try {
        if (id) {
          await customerService.update(id, values as CustomerInterface);
          toast.success("Staff updated successfully");
        } else {
          await customerService.add(values as CustomerInterface);
          toast.success("Staff added successfully");
        }
        navigate("/customers");
      } catch (err) {
        console.error("Error submitting form:", err);
        toast.error("Failed to submit form");
      }
    },
  });

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isAdd: true, entityPath: "/customers", entities: "Customers" })} />

      <Card className="custom-card">
        <Card.Header>
          <h4 className="mb-0">{id ? translate("staff.edit") : translate("staff.add")}</h4>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row>
              <Col md="6">
                <FloatingLabel
                  controlId="firstName"
                  label={translate("staff.name")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="firstName"
                    value={formik.values.firstName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.firstName && !!formik.errors.firstName}
                    autoComplete="off"
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.firstName && formik.errors.firstName}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="lastName"
                  label={translate("staff.name")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="lastName"
                    value={formik.values.lastName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.lastName && !!formik.errors.lastName}
                    autoComplete="off"
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.lastName && formik.errors.lastName}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel
                  controlId="email"
                  label={translate("staff.email")}
                  className="mb-3"
                >
                  <Form.Control
                    type="email"
                    name="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.email && !!formik.errors.email}
                    autoComplete="off"
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.email && formik.errors.email}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              {isNewCustomer && <Col md="6">
                <FloatingLabel
                  controlId="password"
                  label={translate("staff.password")}
                  className="mb-3"
                >
                  <Form.Control
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formik.values.password}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isInvalid={
                      formik.touched.password && !!formik.errors.password
                    }
                    autoComplete="off"
                  />
                  <Button
                    className="eye-icon"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.password && formik.errors.password}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
              }

              <Col md="6">
                <FloatingLabel
                  controlId="phone"
                  label={translate("staff.phone")}
                  className="mb-3"
                >
                  <Form.Control
                    type="text"
                    name="phone"
                    value={formik.values.phone}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, "");
                      formik.setFieldValue("phone", value);
                    }}
                    onBlur={formik.handleBlur}
                    isInvalid={formik.touched.phone && !!formik.errors.phone}
                    autoComplete="off"
                  />
                  <Form.Control.Feedback type="invalid">
                    {formik.touched.phone && formik.errors.phone}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>

              <Col md="12">
                <Form.Group className="mb-3">
                  <Form.Label>{translate("staff.upload-image")}</Form.Label>
                  <ImageUploadComponent
                    bucketType="staffs"
                    handleOnChange={(e: any) => formik.handleChange(e)}
                    image={formik.values.avatar}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("avatar", "")}
                    fieldName="avatar"
                  />
                  {formik.touched.avatar && formik.errors.avatar && (
                    <small className="text-danger">
                      {formik.errors.avatar}
                    </small>
                  )}
                </Form.Group>
              </Col>

              <Col md="12" className="text-end">
                <Button variant="primary" type="submit">
                  Submit
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
    </>
  );
};

export default AddCustomer;
