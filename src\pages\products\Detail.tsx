import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Card, Button, ListGroup, Row, Col } from "react-bootstrap";
import { FaEdit } from "react-icons/fa";
import { productService } from "services";
import { ProductInterface } from "interfaces";
import StatusComponent from "components/StatusComponent";
import ImageModal from "components/ImageModal";
import useTranslate from "hooks/useTranslate";
import './ProductDetail.css';
import BreadcrumbComponent from "components/Breadcrumb";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
// import { productDetailBreadcrumbs } from "constants/breadcrums";

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { translate } = useTranslate();
  const [product, setProduct] = useState<ProductInterface | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchProductDetails = async () => {
      setLoading(true);
      try {
        const response = await productService.getDetail(Number(id));
        setProduct(response.data.data.product);
      } catch (error) {
        console.error("Error fetching product details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProductDetails();
  }, [id]);

  if (loading) {
    return <div className="text-center">{translate("common.loading")}</div>;
  }

  if (!product) {
    return <div className="text-center">{translate("common.notFound")}</div>;
  }

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isDetail: true, entityPath: "/products", entities: "Products" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">{translate("product.details")}</h4>
            <Button
              variant="primary"
              onClick={() => navigate(`/products/${id}/edit`)}
            >
              <FaEdit /> {translate("common.edit")}
            </Button>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="flex-column align-items-start">
            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.title")}: {product.title || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.type")}: {product.type || "N/A"}</p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.description")}: {product.description || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.price")}: ${product.price || "N/A"}</p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.productType")}: {product.productType || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.isCustomizable")}: {product.isCustomizable ? "Yes" : "No"}</p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.isTreatYourSelf")}: {product.isTreatYourSelf ? "Yes" : "No"}</p>
                </div>
              </Col>
              {/* <div className="mb-3">
              <p>{translate("product.isSuggested")}: {product.isSuggested ? "Yes" : "No"}</p> 
            </div> */}
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.menuCategories")}: {product.menuCategoryTitle || "N/A"}</p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.subMenuCategories")}: {product.subMenuCategoryTitle || "N/A"}</p>
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p>{translate("product.suggestionProducts")}: {(product.suggestionProductTitle && product.suggestionProductTitle.length > 0) ? product.suggestionProductTitle.join(', ') : "N/A"}</p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3 d-flex">
                  <p>{translate("common.status")}: <StatusComponent isSuspended={!!product.isSuspended} /> </p>
                </div>
              </Col>
            </Row>

            <Row>
              <Col md="6">
                <div className="mb-3">
                  <p><strong>{translate("product.image")}:</strong></p>
                  <ImageModal
                    height={"150px"}
                    thumbnailSrc={product.image}
                    altText={product.title}
                  />
                </div>
              </Col>
              <Col md="6">
                <div className="mb-3">
                  <p><strong>{translate("product.background-image")}:</strong></p>
                  <ImageModal
                    height={"150px"}
                    thumbnailSrc={product.backgroundImage}
                    altText={product.title}
                  />
                </div>
              </Col>
            </Row>

            {/* <div className="mb-3">
              <p><strong>{translate("product.customizations")}:</strong></p>
              <ListGroup>
                {product.productCustomizations.length > 0 ? (
                  product.productCustomizations.map((customization) => (
                    <ListGroup.Item key={customization.id} className="d-flex align-items-center">
                      <div className="ms-3">
                        {customization.label}
                      </div>
                      {" "}
                      <div className="ms-3">
                        {customization.isRequired ? "Yes" : "No"}
                      </div>
                      {" "}
                      <div className="ms-3">
                        {customization.items.length > 0 ? (
                          customization.items.map((item) => (
                            <ListGroup.Item key={item.itemId} className="d-flex align-items-center">
                              <div className="ms-3">
                                {item.label}
                              </div>
                              {" "}
                              <div className="ms-3">
                                {item.isAdded ? "Yes" : "No"}
                              </div>
                              {" "}
                            </ListGroup.Item>
                        ))
                        ) : (
                          <ListGroup.Item>{translate("product.noItems")}</ListGroup.Item>
                        )}
                      </div>
                    </ListGroup.Item>
                  ))
                ) : (
                  <ListGroup.Item>{translate("product.noCustomizations")}</ListGroup.Item>
                )}
              </ListGroup>
            </div> */}

            <Row>
              <Col md="12">
                <div className="mb-3">
                  <p><strong>{translate("product.customizations")}:</strong></p>
                  <ListGroup className="custom-list-group">
                    {product.productCustomizations.length > 0 ? (
                      product.productCustomizations.map((customization) => (
                        <ListGroup.Item key={customization.id} className="d-flex flex-column flex-md-row align-items-center custom-list-item">
                          <div className="ms-3">
                            <strong>{translate("product.customizationLabel")}:</strong> {customization.label}
                          </div>
                          <div className="ms-3">
                            <strong>{translate("product.isRequired")}:</strong> {customization.isRequired ? "Yes" : "No"}
                          </div>
                          <div className="ms-3">
                            {customization.items.length > 0 ? (
                              customization.items.map((item) => (
                                <ListGroup.Item key={item.itemId} className="d-flex align-items-center custom-sub-item">
                                  <div className="ms-3">
                                    <strong>{translate("product.itemLabel")}:</strong> {item.label}
                                  </div>
                                  <div className="ms-3">
                                    <strong>{translate("product.isAdded")}:</strong> {item.isAdded ? "Yes" : "No"}
                                  </div>
                                </ListGroup.Item>
                              ))
                            ) : (
                              <ListGroup.Item className="no-items">{translate("product.noItems")}</ListGroup.Item>
                            )}
                          </div>
                        </ListGroup.Item>
                      ))
                    ) : (
                      <ListGroup.Item className="no-customizations">{translate("product.noCustomizations")}</ListGroup.Item>
                    )}
                  </ListGroup>
                </div>
              </Col>
            </Row>



          </div>
        </Card.Body>
      </Card>
      {/* </div> */}
    </>
  );
};

export default ProductDetail;
