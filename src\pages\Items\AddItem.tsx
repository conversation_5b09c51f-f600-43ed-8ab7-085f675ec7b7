import BreadcrumbComponent from "components/Breadcrumb";
import { useFormik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON>ge, <PERSON>ton, Card, Col, FloatingLabel, Form, Row } from "react-bootstrap";
import { toast } from "react-toastify";
import * as yup from "yup";
import ImageUploadComponent from "components/ImageUploadComponent"; // Update this import if needed
import { customizationService, itemService } from "services";
import { CustomizationInterface, ItemInterface } from "interfaces";
// import { itemAddBreadcrumbs } from "constants/breadcrums";
import ItemType from "./ItemType";
import ItemSize from "./ItemSize";
import alertService from "services/alert.service";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import { FaInfoCircle } from "react-icons/fa";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { useEffect, useState } from "react";
import { LayerType } from "constants/Dropdow";
import { ImagePaths } from "constants/ImagePaths";
import useTranslate from "hooks/useTranslate";


interface AddItemProps {
  isEdit: boolean
}

const AddItem = ({ isEdit }: AddItemProps) => {
  const navigate = useNavigate();
  const { translate } = useTranslate();
  const [customizationList, setCustomizationList] = useState<CustomizationInterface[]>([]);
  const page = 1;
  const [isMultiSize, setMultiSize] = useState(false);
  const { id } = useParams<{ id: string }>();
  const [selectedLayer, setSelectedLayer] = useState<Number>(0)
  const [amountId, setAmountId] = useState<any>([0, 0, 0])

  const [initialValues, setInitialValues] = useState<any>({
    title: "",
    description: "",
    price: 0,
    image: "",
    types: [],
    sizes: [],
    customizationId: 0,
    isMultiSize: false,
    isMultiType: false,
    isLayering: false,
    isDouble: false,
    isAmountSelectionAvailable: false,
    layerType: "STANDARD",
    layerImage: "NORMAL",
    leftTop: "",
    rightTop: "",
    middle: "",
    layerTitle: "",
    light: 0,
    justRight: 0,
    extra: 0
  });



  const formik: any = useFormik({
    initialValues,
    validationSchema: yup.object().shape({
      title: yup.string().trim().required("Name is required")
        .min(3, "Name must be at least 3 characters")
        .max(50, "Name cannot exceed 50 characters"),
      description: yup.string().trim().required("Description is required")
        .min(3, "Description must be at least 3 characters")
        .max(200, "Description cannot exceed 200 characters"),
      price: yup
        .number()
        .required("Price is required")
        .min(0, "Price must be greater than or equal to 0")
        .max(999999, "Price must be lower than or equal to 999999"),
      image: yup.string().required("Image URL is required"),
      types: yup.array().of(
        yup.object().shape({
          title: yup.string().trim().required("Name is required"),
          image: yup.string().required("Image URL is required"),
          price: yup
            .number()
            .required("Price is required")
            .min(0, "Price must be greater than or equal to 0")
            .max(999999, "Price must be lower than or equal to 999999"),
        })
      ),
      itemType: yup.string()
        .required('Item Type is required')
        .oneOf(["STANDARD", "PREMIUM"]?.map(item => item), 'Invalid item selected'),
      isLayering: yup.string(),
      layerImage: yup
        .string()
        .nullable()
        .when("isLayering", {
          is: false,
          then: (schema) => schema.required("Layer Image is required"),
          otherwise: (schema) => schema.strip(),
        }),
      isDouble: yup.string(),
      isAmountSelectionAvailable: yup.string(),
      light: yup.number().when("isAmountSelectionAvailable", {
        is: (value: any) => value === "true",
        then: () => yup.number().required("Light is required"),
        otherwise: () => yup.number()
      }),
      justRight: yup.number().when("isAmountSelectionAvailable", {
        is: (value: any) => value === "true",
        then: () => yup.number().required("Just Right is required"),
        otherwise: () => yup.number()
      }),
      extra: yup.number().when("isAmountSelectionAvailable", {
        is: (value: any) => value === "true",
        then: () => yup.number().required("Extra is required"),
        otherwise: () => yup.number()
      }),
      layerTitle: yup.string().when("isLayering", {
        is: (value: any) => value === "true",
        then: () => yup.string().required("Layer Title is required"),
        otherwise: () => yup.string().when("isAmountSelectionAvailable", {
          is: (value: any) => value === "true",
          then: () => yup.string().required("Layer Title is required"),
          otherwise: () => yup.string().nullable()
        })
      }),
      leftTop: yup.string().when("isLayering", {
        is: (value: any) => value === "true",
        then: () => yup.string().required("Left Top Title is required"),
        otherwise: () => yup.string()
      }),

      rightTop: yup.string().when("isLayering", {
        is: (value: any) => value === "true",
        then: () => yup.string().required("Right Top Title is required"),
        otherwise: () => yup.string()
      }),
      middle: yup.string().when("isLayering", {
        is: (value: any) => value === "true",
        then: () => yup.string(),
        otherwise: () => yup.string()
      }),
      // LayerType: yup.string().when("isLayering", {
      //   is: (value: any) => value === true,
      //   then: () => yup.string().required("Select One Of Them"),
      //   otherwise: () => yup.string()
      // }),
      sizes: yup.array().of(
        yup.object().shape({
          title: isMultiSize ? yup.string().trim().required("Name is required") : yup.string().trim().optional(),
          image: isMultiSize ? yup.string().required("Image URL is required") : yup.string().optional(),
          price: isMultiSize ? yup
            .number()
            .required("Price is required")
            .min(0, "Price must be greater than or equal to 0")
            .max(999999, "Price must be lower than or equal to 999999") : yup.number().optional()

        })
      ),
      customizationId: yup.number()
        .required('Item selection is required')
        .oneOf(customizationList?.map(item => item.id), 'Invalid item selected'),
    }),
    onSubmit: async (values) => {
      if (values.isMultiType && values.types.length === 0) {
        alertService.showError("Alert", "Please add types if it is a multiselect item")
        return
      }

      if (values.isMultiSize && values.sizes.length === 0) {
        alertService.showError("Alert", "Please add sizes if it is a multi size item")
        return
      }

      try {
        let result;
        const { leftTop, rightTop, middle, ...payload } = values
        // const layerData: any = `${formik.values.leftTop},${formik.values.middle},${formik.values.rightTop}`

        const trimmedMiddle = middle.trim();
        const layerData: string = [leftTop, trimmedMiddle, rightTop]
          .filter((val, idx) => !(idx === 1 && val === ''))
          .join(',');

        if (isEdit) {
          const selectedAmount: any = [{ type: 'EXTRA', price: formik.values.extra, id: amountId[2] }, { type: 'JUST_RIGHT', price: formik.values.justRight, id: amountId[1] }, { type: 'LIGHT', price: formik.values.light, id: amountId[0] }]
          // const selectedAmount: any = [{ type: 'LIGHT', price: formik.values.light, id: amountId[0] }, { type: 'JUST_RIGHT', price: formik.values.justRight, id: amountId[1] }, { type: 'EXTRA', price: formik.values.extra, id: amountId[2] }]
          result = await itemService.updateItem(id!, { ...payload, ...(formik?.values?.isLayering === true && { layerData }), ...(formik?.values?.isAmountSelectionAvailable === true && { selectedAmount }) }); // Update item with ID
          // result = await itemService.updateItem(id!, formik.values); // Update item with ID
        } else {
          const selectedAmount: any = [{ type: 'EXTRA', price: formik.values.extra, id: amountId[2] }, { type: 'JUST_RIGHT', price: formik.values.justRight, id: amountId[1] }, { type: 'LIGHT', price: formik.values.light, id: amountId[0] }]
          // const selectedAmount: any = [{ type: 'LIGHT', price: formik.values.light }, { type: 'JUST_RIGHT', price: formik.values.justRight }, { type: 'EXTRA', price: formik.values.extra }]
          result = await itemService.add({ ...payload, ...(formik?.values?.isLayering === true && { layerData }), ...(formik?.values?.isAmountSelectionAvailable === true && { selectedAmount }) }); // Update with your dish service method
        }
        if (!result.data.success) {
          throw new Error("Failed to add item");
        }

        toast.success("Item added successfully");
        navigate("/items"); // Update navigation path as needed
      } catch (err) {
        console.error("Error adding item:", err);
        toast.error("Failed to add item");
      }
    },
  });

  const addSizeRow = () => {
    const newTypes = [
      ...formik.values.sizes,
      { title: "", image: "", price: 0 },
    ];
    formik.setFieldValue("sizes", newTypes);
  };

  // const addItemTypeRow = () => {
  //   const newTypes = [
  //     ...formik.values.types,
  //     { title: "", image: "", price: 0 },
  //   ];
  //   formik.setFieldValue("types", newTypes);
  // };


  const fetchCustomization = async () => {
    const payload = {
      page: page,
      pagination: false,

    };
    try {
      const response = await customizationService.list(payload);
      setCustomizationList(response.data.data.customization);
    } catch (error) {
      console.error(error);
    };
  }

  useEffect(() => {
    const fetchItem: any = async () => {
      try {
        const { data: { data: { item } } } = await itemService.getDetail(id!); // Fetch item data by ID

        const fetchedItems: any = item as ItemInterface;
        const isMultiType = fetchedItems.types.length > 0;
        const isMultiSize = fetchedItems.sizes.length > 0;
        const layerData = fetchedItems?.layerData?.split(",")

        setAmountId([fetchedItems?.amountPrices[0]?.id, fetchedItems?.amountPrices[1]?.id, fetchedItems?.amountPrices[2]?.id])

        setSelectedLayer(["TOP_MIDDLE_BOTTOM"].includes(fetchedItems?.layerType) ? 1 : 2)
        if (layerData) {
          setInitialValues({ ...fetchedItems, isMultiSize, isMultiType, leftTop: layerData[0], rightTop: layerData[2], middle: layerData[1] });
        } else {
          setInitialValues({ ...fetchedItems, isMultiSize, isMultiType, leftTop: "", rightTop: "", middle: "" });
        }
        const formikKeys = Object.keys(fetchedItems); // Get the keys of formik state
        formikKeys?.forEach(key => {
          if (key !== "layerData") {
            if ({ ...fetchedItems, isMultiSize, isMultiType }.hasOwnProperty(key)) {
              formik.setFieldValue(key, { ...fetchedItems, isMultiSize, isMultiType }[key]);
            }
          }
        });
        if (layerData) {

          if (layerData.length === 2) {
            formik.setFieldValue("leftTop", layerData[0]);
            formik.setFieldValue("rightTop", layerData[1]);
            formik.setFieldValue("middle", "");
          } else if (layerData.length === 3) {
            formik.setFieldValue("leftTop", layerData[0]);
            formik.setFieldValue("middle", layerData[1]);
            formik.setFieldValue("rightTop", layerData[2]);
          }

        } else {
          formik.setFieldValue("leftTop", "")
          formik.setFieldValue("rightTop", "")
          formik.setFieldValue("middle", "")
        }

        const prices = fetchedItems?.amountPrices || [];

        formik.setFieldValue("light", prices.find((p: any) => p.type === "LIGHT")?.price ?? 0);
        formik.setFieldValue("justRight", prices.find((p: any) => p.type === "JUST_RIGHT")?.price ?? 0);
        formik.setFieldValue("extra", prices.find((p: any) => p.type === "EXTRA")?.price ?? 0);

      } catch (error) {
        console.error("Error fetching item:", error);
        toast.error("Failed to fetch item data");
      }
    };

    if (isEdit) {
      fetchItem();
    }
    fetchCustomization()
  }, [])


  const handleSelectChange = (event: any) => {
    const selectedId = parseInt(event.target.value, 10);
    formik.setFieldValue('customizationId', selectedId)
  };

  const handleSelectItemType = (event: any) => {
    formik.setFieldValue('itemType', event.target.value)
  }

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs(isEdit ? { isEdit: true, entityPath: "/items", entities: "Items" } : { isAdd: true, entityPath: "/items", entities: "Items" })} />
      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex gap-2 align-items-center custom-card-header-title">
            <h4 className="mb-0">{isEdit ? 'Update Item' : 'Add Item'}</h4>
          </div>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Row className="align-items-center">
              <Col md="6">
                <FloatingLabel controlId="title" label="Title" className="mb-3">
                  <Form.Control
                    type="text"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.title && formik.errors.title ? (
                    <small className="text-danger">{formik.errors.title}</small>
                  ) : null}
                </FloatingLabel>
              </Col>

              <Col md="6">
                <FloatingLabel controlId="price" label="Price" className="mb-3">
                  <Form.Control
                    type="text" // Keep text to allow decimal input
                    inputMode="decimal"
                    pattern="[0-9]*[.,]?[0-9]*"
                    name="price"
                    value={formik.values.price}
                    onChange={(e) => {
                      // Allow only numbers and a single decimal point
                      const value = e.target.value.replace(/[^0-9.]/g, '');
                      const validValue = value.split('.').length > 2 ? value.slice(0, value.lastIndexOf('.')) : value;
                      formik.setFieldValue("price", validValue);
                    }}
                    onBlur={formik.handleBlur}
                  />

                  {formik.touched.price && formik.errors.price ? (
                    <small className="text-danger">{formik.errors.price}</small>
                  ) : null}
                </FloatingLabel>
              </Col>

              <Col md="6">
                {/* <FloatingLabel
                  controlId="description"
                  label="Description"
                  className="mb-3"
                > */}
                <Form.Control
                  as="textarea"
                  name="description"
                  placeholder="Description"
                  value={formik.values.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  rows={3}
                />
                {formik.touched.description && formik.errors.description ? (
                  <small className="text-danger">
                    {formik.errors.description}
                  </small>
                ) : null}
                {/* </FloatingLabel> */}
              </Col>

              {/* Checkboxes */}
              <Col md="6" className="d-flex align-items-center">
                <Col md={6} lg={4}>
                  <Form.Select onChange={handleSelectItemType} value={formik.values.itemType}
                    className="form-control"
                  >
                    <option value={0}>Select Item Type</option>
                    {["STANDARD", "PREMIUM"]?.map(item => (
                      <option key={item} value={item}>
                        {item}
                      </option>

                    ))}
                    {formik.touched.itemType && formik.errors.itemType ? (
                      <small className="text-danger">{formik.errors.itemType}</small>
                    ) : null}
                  </Form.Select>
                </Col>

                {/* <Col md="4" className="d-flex align-items-center">
                  <Form.Check
                    type="checkbox"
                    id="isMultiSize"
                    name="isMultiSize"
                    label="Multi Size"
                    checked={formik.values.isMultiSize}
                    onChange={(e) => {
                      formik.handleChange(e);
                      setMultiSize(formik.values.isMultiSize ? true : false)
                    }}
                  />
                  <div className="d-flex align-items-center ms-2">
                    <FaInfoCircle
                      className="info-icon"
                      data-tooltip-id="isMultiSize-tooltip"
                      data-tooltip-content="This will allow you to add multiple sizes"
                    />
                    <Tooltip
                      id="isMultiSize-tooltip"
                      place="top"
                    />
                  </div>
                </Col> */}
              </Col>

              <Col md="6">
                <Form.Group className="mb-3">
                  <Form.Label>Upload Image</Form.Label>
                  <ImageUploadComponent
                    bucketType="items"
                    handleOnChange={(e) => formik.handleChange(e)}
                    image={formik.values.image}
                    setImage={(field, value) =>
                      formik.setFieldValue(field, value)
                    }
                    handleRemove={() => formik.setFieldValue("image", "")}
                    fieldName="image"
                  />
                  {formik.touched.image && formik.errors.image ? (
                    <small className="text-danger">{formik.errors.image}</small>
                  ) : null}
                </Form.Group>
              </Col>
            </Row>
            <Row className="my-3">
              <Col>
                <h5>Feature Settings</h5>
              </Col>
            </Row>
            <Row className="mt-4">

              <Col>
                <Form.Check
                  type="switch"
                  id="layering-switch"
                  label="is layering allowed?"
                  checked={formik.values.isLayering}

                  onChange={(event) => {
                    formik.setFieldValue('isLayering', event?.target?.checked)
                    if (event?.target?.checked) {
                      formik.setFieldValue('isDouble', false)
                      formik.setFieldValue('isAmountSelectionAvailable', false)
                    }
                  }}
                />
              </Col>

              <Col>
                <Form.Check
                  type="switch"
                  id="double-switch"
                  label="is Double allowed?"
                  checked={formik.values.isDouble}
                  onChange={(event) => {
                    formik.setFieldValue('isDouble', event?.target?.checked)
                    if (event?.target?.checked) {
                      formik.setFieldValue('isLayering', false)
                      formik.setFieldValue('isAmountSelectionAvailable', false)
                    }
                  }}
                />
              </Col>

              <Col>
                <Form.Check
                  type="switch"
                  id="amount-switch"
                  label="is Amount Selection allowed?"
                  checked={formik.values.isAmountSelectionAvailable}
                  onChange={(event) => {
                    formik.setFieldValue('isAmountSelectionAvailable', event?.target?.checked)
                    if (event?.target?.checked) {
                      formik.setFieldValue('isLayering', false)
                      formik.setFieldValue('isDouble', false)
                    }
                  }}
                />
              </Col>
            </Row>

            {
              formik?.values?.isLayering &&
              <Row style={{ marginTop: "20px" }}>
                {/* <Col>
                  <p>Select layer</p>
                </Col>
                <Col>
                  <Form.Check
                    type="switch"
                    id="amount-switch"
                    label="Top Middle Bottom"
                    checked={selectedLayer === 1}
                    onChange={(event) => {
                      formik.setFieldValue('layerType', event?.target?.checked ? "TOP_MIDDLE_BOTTOM" : "TOP_BOTTOM")
                      if (event?.target?.checked) {
                        setSelectedLayer(1)
                      } else {
                        setSelectedLayer(2)
                        formik.setFieldValue('layerType', "TOP_BOTTOM")
                      }
                    }}
                  />
                </Col>
                <Col>
                  <Form.Check
                    type="switch"
                    id="amount-switch"
                    label="Top Bottom"
                    checked={selectedLayer === 2}
                    onChange={(event) => {
                      formik.setFieldValue('layerType', !event?.target?.checked ? "TOP_MIDDLE_BOTTOM" : "TOP_BOTTOM")
                      if (event?.target?.checked) {
                        setSelectedLayer(2)
                      } else {
                        formik.setFieldValue('layerType', "TOP_MIDDLE_BOTTOM")
                        setSelectedLayer(1)
                      }
                    }}
                  />
                </Col> */}

                <Row style={{ marginTop: "20px" }}>
                  <Col md="12">
                    <Form.Group className="mb-3">
                      <Form.Label>{translate("menu.layerImage")}</Form.Label>
                      <div className="d-flex gap-4 align-items-center">
                        <Form.Check
                          type="radio"
                          name="layoutImage"
                          id="normal-image"
                          value="NORMAL"
                          checked={formik.values.layerImage === "NORMAL"}
                          onChange={() => formik.setFieldValue("layerImage", "NORMAL")}
                          label={
                            <img
                              src={ImagePaths.normalLayer}
                              alt="Normal layer"
                              style={{
                                width: "100%",
                                maxWidth: "250px",
                                height: "200px",
                                cursor: "pointer",
                                border:
                                  formik.values.layerImage === "NORMAL"
                                    ? "2px solid black"
                                    : "2px dashed black",
                                objectFit: "contain",
                              }}
                            />
                          }
                        />
                        <Form.Check
                          type="radio"
                          name="layerImage"
                          id="yogurt-image"
                          value="YOGURT"
                          checked={formik.values.layerImage === "YOGURT"}
                          onChange={() => formik.setFieldValue("layerImage", "YOGURT")}
                          label={
                            <img
                              src={ImagePaths.yogurtImage}
                              alt="Yogurt layer"
                              style={{
                                width: "100%",
                                maxWidth: "250px",
                                height: "200px",
                                cursor: "pointer",
                                border:
                                  formik.values.layerImage === "YOGURT"
                                    ? "2px solid black"
                                    : "2px dashed black",
                                objectFit: "contain",
                              }}
                            />
                          }
                        />
                        <Form.Check
                          type="radio"
                          name="layerImage"
                          id="ice-cream-image"
                          value="ICECREAM"
                          checked={formik.values.layerImage === "ICECREAM"}
                          onChange={() => formik.setFieldValue("layerImage", "ICECREAM")}
                          label={
                            <img
                              src={ImagePaths.ice_cream}
                              alt="ice cream layer"
                              style={{
                                width: "100%",
                                maxWidth: "250px",
                                height: "200px",
                                cursor: "pointer",
                                border:
                                  formik.values.layerImage === "ICECREAM"
                                    ? "2px solid black"
                                    : "2px dashed black",
                                objectFit: "contain",
                              }}
                            />
                          }
                        />
                      </div>
                    </Form.Group>
                  </Col>
                </Row>

                <Row>

                  <Col md={12}>
                    <FloatingLabel controlId="layerTitle" label="Layer Title" className="mb-3">
                      <Form.Control
                        type="text"
                        {...formik.getFieldProps("layerTitle")}
                      // name="layerTitle"
                      // value={formik.values.layerTitle}
                      // onChange={formik.handleChange}
                      // onBlur={formik.handleBlur}
                      />
                      {formik.touched.layerTitle && formik.errors.layerTitle ? (
                        <small className="text-danger">{formik.errors.layerTitle}</small>
                      ) : null}
                    </FloatingLabel>
                  </Col>

                  <Col md="4">
                    <FloatingLabel controlId="LeftTop" label="Left Top Title" className="mb-3">
                      <Form.Control
                        type="text"
                        {...formik.getFieldProps("leftTop")}
                      // name="leftTop"
                      // value={formik.values.leftTop}
                      // onChange={formik.handleChange}
                      // onBlur={formik.handleBlur}
                      />
                      {formik.touched.leftTop && formik.errors.leftTop ? (
                        <small className="text-danger">{formik.errors.leftTop}</small>
                      ) : null}
                    </FloatingLabel>
                  </Col>

                  <Col md="4">
                    <FloatingLabel controlId="middle" label="Middle Title" className="mb-3">
                      <Form.Control
                        type="text"
                        {...formik.getFieldProps("middle")}
                      />
                      {formik.touched.middle && formik.errors.middle ? (
                        <small className="text-danger">{formik.errors.middle}</small>
                      ) : null}
                    </FloatingLabel>
                  </Col>

                  <Col md="4">
                    <FloatingLabel controlId="rightTop" label="Right Top Title" className="mb-3">
                      <Form.Control
                        type="text"
                        {...formik.getFieldProps("rightTop")}
                      // name="rightTop"
                      // value={formik.values.rightTop}
                      // onChange={formik.handleChange} 
                      // onBlur={formik.handleBlur}
                      />
                      {formik.touched.rightTop && formik.errors.rightTop ? (
                        <small className="text-danger">{formik.errors.rightTop}</small>
                      ) : null}
                    </FloatingLabel>
                  </Col>
                </Row>
              </Row>
            }

            {
              formik?.values?.isAmountSelectionAvailable &&

              <Row style={{ marginTop: "20px" }}>

                <Col md={12}>
                  <FloatingLabel controlId="layerTitle" label="Layer Title" className="mb-3">
                    <Form.Control
                      type="text"
                      {...formik.getFieldProps("layerTitle")}
                    // name="layerTitle"
                    // value={formik.values.layerTitle}
                    // onChange={formik.handleChange}
                    // onBlur={formik.handleBlur}
                    />
                    {formik.touched.layerTitle && formik.errors.layerTitle ? (
                      <small className="text-danger">{formik.errors.layerTitle}</small>
                    ) : null}
                  </FloatingLabel>
                </Col>


                <Row>
                  <Col md={4}>
                    <FloatingLabel controlId="light" label="Light" className="mb-3">
                      <Form.Control
                        type="number"
                        {...formik.getFieldProps("light")}
                      />
                      {formik.touched.light && formik.errors.light ? (
                        <small className="text-danger">{formik.errors.light}</small>
                      ) : null}
                    </FloatingLabel>
                  </Col>

                  <Col md="4">
                    <FloatingLabel controlId="justRight" label="Just Right" className="mb-3">
                      <Form.Control
                        type="text"
                        {...formik.getFieldProps("justRight")}
                      />
                      {formik.touched.justRight && formik.errors.justRight ? (
                        <small className="text-danger">{formik.errors.justRight}</small>
                      ) : null}
                    </FloatingLabel>
                  </Col>

                  <Col md="4">
                    <FloatingLabel controlId="extra" label="Extra" className="mb-3">
                      <Form.Control
                        type="text"
                        {...formik.getFieldProps("extra")}
                      />
                      {formik.touched.extra && formik.errors.extra ? (
                        <small className="text-danger">{formik.errors.extra}</small>
                      ) : null}
                    </FloatingLabel>
                  </Col>

                </Row>
              </Row>
            }

            <Row className="mt-4">
              <p>Select Customization</p>
              <Col lg={4}>
                <Form.Select onChange={handleSelectChange} value={formik.values.customizationId}
                  className="form-control"
                >
                  <option value={0}>Select Customization</option>
                  {customizationList?.map(item => (
                    <option key={item.id} value={item.id}>
                      {item.title}
                    </option>

                  ))}
                </Form.Select>
              </Col>
              {formik.touched.customizationId && formik.errors.customizationId && (
                <small className="text-danger">
                  {formik.errors.customizationId}
                </small>
              )}
            </Row>

            <Row>
              {formik.values.isMultiSize && (
                <Col md="6" className="mt-4">
                  <h5>Sizes</h5>
                  {formik.values.sizes.map((item: any, index: any) => (
                    <ItemSize
                      key={index}
                      index={index}
                      item={item}
                      changeHandler={(i, value, fieldName) =>
                        formik.setFieldValue(`sizes[${index}].${fieldName}`, value)
                      }
                      onRemove={(i) =>
                        formik.setFieldValue(
                          "sizes",
                          formik.values.sizes.filter((_: any, i: any) => i !== index)
                        )
                      }
                      errors={formik?.errors?.sizes?.[index]}
                    />
                  ))}
                  <Button
                    type="button"
                    onClick={addSizeRow}
                    variant="secondary"
                  >
                    Add Size
                  </Button>
                </Col>
              )}
            </Row>


            <Row>
              <Col md="12" className="text-end">
                <Button variant="primary"
                  type="submit"
                // onClick={(e) => {
                //   e.preventDefault();
                //   formik.handleSubmit();
                // }}
                >
                  Submit
                </Button>
              </Col>
            </Row>

          </Form>
        </Card.Body >
      </Card >
    </>
  );
};

export default AddItem;
