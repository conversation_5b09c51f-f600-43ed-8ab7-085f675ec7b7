import base from "./base.service";
import { StaffInterface } from "interfaces";

const getAll = (payload: any) => base.get("/pages", { params: payload });
const updateStatus = (id: number | string) =>
    base.get(`/staff/${id}/update-status`);
const remove = (id: number | string) => base.delete(`/staff/${id}`);
const add = (payload: StaffInterface) => base.post(`/staff`, { ...payload });
const getDetail = (id: string | number) => base.get(`pages/${id}/info`);
const update = (payload: any) =>
    base.put(`pages/${payload?.id}`, { ...payload });
const permissionList = () => base.get("/staff/permission-list");

export const StaticService = {
    getAll,
    updateStatus,
    remove,
    add,
    getDetail,
    update,
    permissionList,
};
