import { Navigate, Outlet } from "react-router-dom";
import Header from "./Header";
import Sidebar from "./Sidebar";
import "styles/main-content.scss";
import { useEffect, useState } from "react";
import useCurrentUser from "hooks/useCurrentUser";

const ProtectedLayout = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const token = localStorage.getItem("token");
  const { setToken } = useCurrentUser();

  useEffect(() => {
    setToken(token);
  }, [token]);

  if (!token) {
    return <Navigate to="/" />;
  }
  return (
    <>
      <Header
        isSidebarCollapsed={isSidebarCollapsed}
        setIsSidebarCollapsed={setIsSidebarCollapsed}
      />
      <Sidebar
        isSidebarCollapsed={isSidebarCollapsed}
        setIsSidebarCollapsed={setIsSidebarCollapsed}
      />
      <div
        className={`main-content ${isSidebarCollapsed ? "menu-collapsed" : ""}`}
      >
        <Outlet />
      </div>
    </>
  );
};

export default ProtectedLayout;
