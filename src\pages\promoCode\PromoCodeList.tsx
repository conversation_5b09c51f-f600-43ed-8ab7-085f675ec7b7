import AddActionComponent from "components/AddActionComponent";
import BreadcrumbComponent from "components/Breadcrumb";
import DatatableComponent from "components/DatatableComponent";
import SearchInputComponent from "components/SearchInputComponent";
import { getBreadcrumbs } from "components/Breadcrumb/validator";
import { MenuCategoryInterface, PromoCodeInterface } from "interfaces";
import { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { FaList } from "react-icons/fa";
import {promoCodeService } from "services";
import { toast } from "react-toastify";
import DeleteActionComponent from "components/DeleteActionComponent";
import DeleteModal from "components/Modal/DeleteModal";
import EditActionComponent from "components/EditActionComponent";
import { getFormattedDate } from "utils/helpers";
import StatusActionComponent from "components/StatusActionComponent";

const PromoCodeList = () => {
  const [data, setData] = useState<MenuCategoryInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState("id");
  const [sortDirection, setSortDirection] = useState("DESC");
  const [searchTerm, setSearchTerm] = useState("");
  const [show, setShow] = useState<any>(false);
  const [id, setId] = useState<any>("");
  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);


  const fetchData = async () => {
    setLoading(true);
    const payload = {
      page,
      pagination: true,
      skip: (page - 1) * perPage,
      limit: perPage,
      sortBy,
      sortDirection,
      searchTerm,
      activeStatus: "ALL",
      isParent: true
    };
    try {
      const response = await promoCodeService.getPromoCodeList(payload);
      if (response?.data?.success) {
        setData(response.data.data.docs);
        setTotalRows(response.data.data.meta.totalDocs);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (id: number | string) => {
    try {
      await promoCodeService.updatePromoCodeStatus(id);
      setData(prevData =>
        prevData.map(category =>
          category.id === id ? { ...category, isSuspended: !category.isSuspended } : category
        )
      );
    } catch (error) {
      console.error(error);
    }
  };
  const handleDelete = async (id: any) => {
    try {
      await promoCodeService.deleteMenuLayout(id).then((response) => {
        toast.success(response?.data?.message);
      }).catch((error) => {
        toast.error(error?.data?.message);
      });
      setData(prevData => prevData.filter(staff => staff.id !== id));
    } catch (error) {
      console.error(error);
    }
    handleClose(); // Close the modal after delete
  };


  const removeRecord = async (id: number | string) => {
    setId(id)
    handleShow()

  };
  const renderActionColumn = (row: PromoCodeInterface) => {
    return (
      <>
        <StatusActionComponent
                id={row.id}
                isSuspended={row.isSuspended}
                updateStatus={() => updateStatus(row.id)}
              />{" "}
        <EditActionComponent url={`/promo-code/${row.id}/edit`}></EditActionComponent>{" "}
        <DeleteActionComponent fn={() => removeRecord(Number(row.id))} />{" "}
      </>
    );
  };

  const columns = [
    {
      name: "Title",
      sortable: true,
      sortField: "title",
      selector: (row: any) => row?.title || "N/A",
    },
    {
      name: "PromoCode",
      sortable: true,
      sortField: "promoCode",
      selector: (row: any) => row?.promoCode || "N/A",
    },
    {
      name: "Discount Type",
      sortable: true,
      sortField: "discountType",
      selector: (row: any) => row?.discountType || "N/A",
    },
    {
      name: "Discount",
      sortable: true,
      sortField: "discount",
      selector: (row: any) => row?.discount || "-",
    },
    {
      name: "Start Date",
      sortable: true,
      sortField: "startDate",
      selector: (row: any) => getFormattedDate(row?.startDate) || "N/A",
    },
    {
      name: "End Date",
      sortable: true,
      sortField: "endDate",
      selector: (row: any) => getFormattedDate(row?.endDate)  || "N/A",
    },
    {
      name: "Action",
      selector: (row: any) => renderActionColumn(row),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [page, perPage, sortBy, sortDirection, searchTerm]);

  return (
    <>
      <BreadcrumbComponent breadcrumbs={getBreadcrumbs({ isList: true, entityPath: "/menu-layout", entities: "Promo Code Listing" })} />

      <Card className="custom-card">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center custom-card-header-title">
              <FaList />
              <h4 className="mb-0">Promo Code</h4>
            </div>
            <div>
              <AddActionComponent url="/promo-code/add" />
            </div>
          </div>
        </Card.Header>

        <Card.Body>
          <div className="d-flex gap-3 pb-4 search-box">
            <SearchInputComponent
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          <DatatableComponent
            columns={columns}
            data={data}
            loading={loading}
            totalRows={totalRows}
            setPage={setPage}
            setPerPage={setPerPage}
            setSortBy={setSortBy}
            setSortDirection={setSortDirection}
          />

          <DeleteModal title="Menu Layout" id={id} show={show} handleDelete={handleDelete} handleClose={handleClose} />
        </Card.Body>
      </Card>
    </>
  );
};

export default PromoCodeList;
